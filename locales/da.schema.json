/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Farver",
      "settings": {
        "background": {
          "label": "Baggrund"
        },
        "background_gradient": {
          "label": "Baggrundsgraduering",
          "info": "Baggrundsgraduering erstatter baggrunden, hvor det er muligt."
        },
        "text": {
          "label": "Tekst"
        },
        "button_background": {
          "label": "Udfyldt knapbaggrund"
        },
        "button_label": {
          "label": "Udfyldt knaptekst"
        },
        "secondary_button_label": {
          "label": "Rammeknap"
        },
        "shadow": {
          "label": "Skygge"
        }
      }
    },
    "typography": {
      "name": "Typografi",
      "settings": {
        "type_header_font": {
          "label": "Skrifttype"
        },
        "header__1": {
          "content": "Overskrifter"
        },
        "header__2": {
          "content": "Brødtekst"
        },
        "type_body_font": {
          "label": "Skrifttype"
        },
        "heading_scale": {
          "label": "Skalering"
        },
        "body_scale": {
          "label": "Skalering"
        }
      }
    },
    "social-media": {
      "name": "Sociale medier",
      "settings": {
        "social_twitter_link": {
          "label": "X/Twitter",
          "info": "https://x.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "https://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "SoMe-konti"
        }
      }
    },
    "currency_format": {
      "name": "Valutaformat",
      "settings": {
        "currency_code_enabled": {
          "label": "Valutakoder"
        },
        "paragraph": "Priser i indkøbskurv og betalingsproces viser altid valutakoder"
      }
    },
    "layout": {
      "name": "Layout",
      "settings": {
        "page_width": {
          "label": "Sidebredde"
        },
        "spacing_sections": {
          "label": "Mellemrum mellem skabelonafsnit"
        },
        "header__grid": {
          "content": "Gitter"
        },
        "paragraph__grid": {
          "content": "Påvirker områder med flere kolonner eller rækker"
        },
        "spacing_grid_horizontal": {
          "label": "Lodret afstand"
        },
        "spacing_grid_vertical": {
          "label": "Vandret afstand"
        }
      }
    },
    "search_input": {
      "name": "Søgeadfærd",
      "settings": {
        "predictive_search_enabled": {
          "label": "Søgeforslag"
        },
        "predictive_search_show_vendor": {
          "label": "Produktforhandler",
          "info": "Vises, når søgeforslag er aktiveret"
        },
        "predictive_search_show_price": {
          "label": "Produktpris",
          "info": "Vises, når søgeforslag er aktiveret"
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Kant"
        },
        "header__shadow": {
          "content": "Skygge"
        },
        "blur": {
          "label": "Slør"
        },
        "corner_radius": {
          "label": "Hjørneradius"
        },
        "horizontal_offset": {
          "label": "Vandret forskydning"
        },
        "vertical_offset": {
          "label": "Lodret forskydning"
        },
        "thickness": {
          "label": "Tykkelse"
        },
        "opacity": {
          "label": "Uigennemsigtighed"
        },
        "image_padding": {
          "label": "Billedmargen"
        },
        "text_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Tekstjustering"
        }
      }
    },
    "badges": {
      "name": "Badges",
      "settings": {
        "position": {
          "options__1": {
            "label": "Nederst til venstre"
          },
          "options__2": {
            "label": "Nederst til højre"
          },
          "options__3": {
            "label": "Øverst til venstre"
          },
          "options__4": {
            "label": "Øverst til højre"
          },
          "label": "Placering på kort"
        },
        "sale_badge_color_scheme": {
          "label": "Farveskema for udsalg-badges"
        },
        "sold_out_badge_color_scheme": {
          "label": "Farveskema for udsolgt-badges"
        }
      }
    },
    "buttons": {
      "name": "Knapper"
    },
    "variant_pills": {
      "name": "Variantetiketter",
      "paragraph": "Variantetiketter er visning af dine [produktvarianter](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block)"
    },
    "inputs": {
      "name": "Inputs"
    },
    "content_containers": {
      "name": "Objektbeholder til indhold"
    },
    "popups": {
      "name": "Rullemenuer og pop-ops",
      "paragraph": "Påvirker områder som navigationsrullemenuer, pop op-modusser og indkøbskurve som pop-ops"
    },
    "media": {
      "name": "Medie"
    },
    "drawers": {
      "name": "Skuffer"
    },
    "cart": {
      "name": "Indkøbskurv",
      "settings": {
        "cart_type": {
          "label": "Type",
          "drawer": {
            "label": "Skuffe"
          },
          "page": {
            "label": "Side"
          },
          "notification": {
            "label": "Pop op-meddelelse"
          }
        },
        "show_vendor": {
          "label": "Forhandler"
        },
        "show_cart_note": {
          "label": "Bemærkning til indkøbskurv"
        },
        "cart_drawer": {
          "header": "Indkøbskurvskuffe",
          "collection": {
            "label": "Kollektion",
            "info": "Vises, når indkøbskurvskuffen er tom"
          }
        }
      }
    },
    "cards": {
      "name": "Produktkort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "collection_cards": {
      "name": "Kollektionskort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "blog_cards": {
      "name": "Blogkort",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Kort"
          },
          "label": "Stil"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Bredde"
        },
        "favicon": {
          "label": "Favoritikon",
          "info": "Vises i 32 x 32 px"
        }
      }
    },
    "brand_information": {
      "name": "Brandoplysninger",
      "settings": {
        "brand_headline": {
          "label": "Overskrift"
        },
        "brand_description": {
          "label": "Beskrivelse"
        },
        "brand_image": {
          "label": "Billede"
        },
        "brand_image_width": {
          "label": "Billedbredde"
        },
        "paragraph": {
          "content": "Vises i sidefodens blok til brandoplysninger"
        }
      }
    },
    "animations": {
      "name": "Animationer",
      "settings": {
        "animations_reveal_on_scroll": {
          "label": "Afslør afsnit ved rulning"
        },
        "animations_hover_elements": {
          "options__1": {
            "label": "Ingen"
          },
          "options__2": {
            "label": "Lodret løft"
          },
          "label": "Svæveeffekt",
          "info": "Påvirker kort og knapper",
          "options__3": {
            "label": "3D-løft"
          }
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Margen",
        "padding_top": "Top",
        "padding_bottom": "Bund"
      },
      "spacing": "Mellemrum",
      "colors": {
        "label": "Farveskema",
        "has_cards_info": "Hvis du vil ændre farveskemaet for kort, skal du opdatere dine temaindstillinger."
      },
      "heading_size": {
        "label": "Størrelse for overskrift",
        "options__1": {
          "label": "Lille"
        },
        "options__2": {
          "label": "Medium"
        },
        "options__3": {
          "label": "Stor"
        },
        "options__4": {
          "label": "Ekstra stor"
        },
        "options__5": {
          "label": "Ekstra, ekstra stor"
        }
      },
      "image_shape": {
        "options__1": {
          "label": "Standard"
        },
        "options__2": {
          "label": "Bue"
        },
        "options__3": {
          "label": "Blob"
        },
        "options__4": {
          "label": "Venstre chevron"
        },
        "options__5": {
          "label": "Højre chevron"
        },
        "options__6": {
          "label": "Diamant"
        },
        "options__7": {
          "label": "Parallelogram"
        },
        "options__8": {
          "label": "Rund"
        },
        "label": "Billedform"
      },
      "animation": {
        "content": "Animationer",
        "image_behavior": {
          "options__1": {
            "label": "Ingen"
          },
          "options__2": {
            "label": "Omgivende bevægelse"
          },
          "label": "Animation",
          "options__3": {
            "label": "Fast baggrundsplacering"
          },
          "options__4": {
            "label": "Zoom ind på rullefelt"
          }
        }
      }
    },
    "announcement-bar": {
      "name": "Meddelelseslinje",
      "blocks": {
        "announcement": {
          "name": "Meddelelse",
          "settings": {
            "text": {
              "label": "Tekstfarve",
              "default": "Velkommen til vores butik"
            },
            "text_alignment": {
              "label": "Tekstjustering",
              "options__1": {
                "label": "Venstre"
              },
              "options__2": {
                "label": "Centreret"
              },
              "options__3": {
                "label": "Højre"
              }
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "settings": {
        "auto_rotate": {
          "label": "Roter meddelelser automatisk"
        },
        "change_slides_speed": {
          "label": "Skift hver"
        },
        "show_social": {
          "label": "Ikoner for sociale medier",
          "info": "[Administrer SoMe-konti](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Lande-/områdevælger",
          "info": "[Adminstrer lande/områder](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Sprogvælger",
          "info": "[Administrer sprog](/admin/settings/languages)"
        },
        "heading_utilities": {
          "content": "Forsyning"
        },
        "paragraph": {
          "content": "Vises kun på store skærme"
        }
      },
      "presets": {
        "name": "Meddelelseslinje"
      }
    },
    "collage": {
      "name": "Collage",
      "settings": {
        "heading": {
          "label": "Overskrift",
          "default": "Multimedie-kollage"
        },
        "desktop_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Stor blok først"
          },
          "options__2": {
            "label": "Stor blok sidst"
          }
        },
        "mobile_layout": {
          "label": "Mobillayout",
          "options__1": {
            "label": "Collage"
          },
          "options__2": {
            "label": "Kolonne"
          }
        },
        "card_styles": {
          "label": "Kortstilart",
          "info": "Administer individuelle kortstilarter i [temaindstillinger](/editor?context=theme&category=product%20cards)",
          "options__1": {
            "label": "Brug individuel kortstil"
          },
          "options__2": {
            "label": "Style alle som produktkort"
          }
        },
        "header_layout": {
          "content": "Layout"
        }
      },
      "blocks": {
        "image": {
          "name": "Billede",
          "settings": {
            "image": {
              "label": "Billede"
            }
          }
        },
        "product": {
          "name": "Produkt",
          "settings": {
            "product": {
              "label": "Produkt"
            },
            "secondary_background": {
              "label": "Vis sekundær baggrund"
            },
            "second_image": {
              "label": "Vis sekundær baggrund, når der peges"
            }
          }
        },
        "collection": {
          "name": "Kollektion",
          "settings": {
            "collection": {
              "label": "Kollektion"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Coverbillede"
            },
            "video_url": {
              "label": "Webadresse",
              "info": "Videoer afspilles i et pop op-vindue, hvis afsnittet indeholder andre blokke.",
              "placeholder": "Brug en YouTube- eller Vimeo-webadresse"
            },
            "description": {
              "label": "Alternativ tekst til video",
              "info": "Beskriv videoen for kunder med en skærmlæser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
              "default": "Beskriv videoen"
            }
          }
        }
      },
      "presets": {
        "name": "Collage"
      }
    },
    "collection-list": {
      "name": "Kollektionsliste",
      "settings": {
        "title": {
          "label": "Overskrift",
          "default": "Kollektioner"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          }
        },
        "swipe_on_mobile": {
          "label": "Karrusel"
        },
        "show_view_all": {
          "label": "Knappen \"Se alle\"",
          "info": "Synlig, hvis en liste indeholder flere kollektioner end vist"
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Kolonner",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_layout": {
          "content": "Layout"
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Kollektion",
          "settings": {
            "collection": {
              "label": "Kollektion"
            }
          }
        }
      },
      "presets": {
        "name": "Kollektionsliste"
      }
    },
    "contact-form": {
      "name": "Kontaktformular",
      "presets": {
        "name": "Kontaktformular"
      },
      "settings": {
        "title": {
          "default": "Kontaktformular",
          "label": "Overskrift"
        }
      }
    },
    "custom-liquid": {
      "name": "Tilpasset Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Liquid-kode",
          "info": "Tilføj appkodestykker eller anden kode for at oprette avancerede tilpasninger. [Få mere at vide](https://shopify.dev/docs/api/liquid)"
        }
      },
      "presets": {
        "name": "Tilpasset Liquid"
      }
    },
    "featured-blog": {
      "name": "Blogopslag",
      "settings": {
        "heading": {
          "label": "Overskrift",
          "default": "Blogopslag"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Antal opslag"
        },
        "show_view_all": {
          "label": "Knappen \"Se alle\"",
          "info": "Synlig, hvis en blog har flere opslag end vist"
        },
        "show_image": {
          "label": "Udvalgt billede"
        },
        "show_date": {
          "label": "Dato"
        },
        "show_author": {
          "label": "Forfatter"
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "layout_header": {
          "content": "Layout"
        },
        "text_header": {
          "content": "Tekst"
        }
      },
      "presets": {
        "name": "Blogopslag"
      }
    },
    "featured-collection": {
      "name": "Udvalgt kollektion",
      "settings": {
        "title": {
          "label": "Overskrift",
          "default": "Udvalgt kollektion"
        },
        "collection": {
          "label": "Kollektion"
        },
        "products_to_show": {
          "label": "Produktantal"
        },
        "show_view_all": {
          "label": "Knappen \"Se alle\"",
          "info": "Synlig, hvis en kollektion har flere produkter end vist"
        },
        "header": {
          "content": "Produktkort"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundær baggrund, når der peges"
        },
        "show_vendor": {
          "label": "Forhandler"
        },
        "show_rating": {
          "label": "Produktbedømmelse",
          "info": "Der kræves en app til bedømmelser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "enable_quick_buy": {
          "label": "Tilføj hurtigt"
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "description": {
          "label": "Beskrivelse"
        },
        "show_description": {
          "label": "Vis kollektionsbeskrivelse fra administrator"
        },
        "description_style": {
          "label": "Beskrivelsesstil",
          "options__1": {
            "label": "Brødtekst"
          },
          "options__2": {
            "label": "Underoverskrift"
          },
          "options__3": {
            "label": "Store bogstaver"
          }
        },
        "view_all_style": {
          "options__1": {
            "label": "Link"
          },
          "options__2": {
            "label": "Rammeknap"
          },
          "options__3": {
            "label": "Udfyldt knap"
          },
          "label": "Stilarten “Se alle”"
        },
        "enable_desktop_slider": {
          "label": "Karrusel"
        },
        "full_width": {
          "label": "Produkter i fuld bredde"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Kolonner",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "swipe_on_mobile": {
          "label": "Karrusel"
        },
        "header_text": {
          "content": "Tekst"
        },
        "header_collection": {
          "content": "Kollektionens layout"
        }
      },
      "presets": {
        "name": "Udvalgt kollektion"
      }
    },
    "footer": {
      "name": "Sidefod",
      "blocks": {
        "link_list": {
          "name": "Menu",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Genvejslinks"
            },
            "menu": {
              "label": "Menu"
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Overskrift"
            },
            "subtext": {
              "label": "Undertekst",
              "default": "<p>Del kontaktoplysninger, butiksoplysninger og brandindhold med dine kunder.</p>"
            }
          }
        },
        "brand_information": {
          "name": "Brandoplysninger",
          "settings": {
            "paragraph": {
              "content": "Administrer brandoplysninger i [temaindstillinger](/editor?context=theme&category=brand%20information)"
            },
            "show_social": {
              "label": "Ikoner for sociale medier",
              "info": "[Administrer SoMe-konti](/editor?context=theme&category=social%20media)"
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Tilmelding med mail"
        },
        "newsletter_heading": {
          "label": "Overskrift",
          "default": "Tilmeld dig vores mails"
        },
        "header__1": {
          "content": "Tilmelding med mail",
          "info": "Tilføjelse af tilmeldinger [kundeprofiler](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "show_social": {
          "label": "Ikoner for sociale medier",
          "info": "[Administrer SoMe-konti](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Lande-/områdevælger",
          "info": "[Adminstrer lande/områder](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Sprogvælger",
          "info": "[Administrer sprog](/admin/settings/languages)"
        },
        "payment_enable": {
          "label": "Ikoner for betalingsmetoder"
        },
        "margin_top": {
          "label": "Øverste margen"
        },
        "show_policy": {
          "label": "Links til politikker",
          "info": "[Administer politikker](/admin/settings/legal)"
        },
        "header__9": {
          "content": "Forsyning"
        },
        "enable_follow_on_shop": {
          "label": "Følg på Shop",
          "info": "Shop Pay skal være aktiveret. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        }
      }
    },
    "header": {
      "name": "Sidehoved",
      "settings": {
        "logo_position": {
          "label": "Logoplacering",
          "options__1": {
            "label": "Midt på til venstre"
          },
          "options__2": {
            "label": "Øverst til venstre"
          },
          "options__3": {
            "label": "Øverst i midten"
          },
          "options__4": {
            "label": "Midt på centreret"
          }
        },
        "menu": {
          "label": "Menu"
        },
        "show_line_separator": {
          "label": "Adskillelseslinje"
        },
        "margin_bottom": {
          "label": "Nederste margen"
        },
        "menu_type_desktop": {
          "label": "Menutype",
          "options__1": {
            "label": "Rullemenu"
          },
          "options__2": {
            "label": "Megamenu"
          },
          "options__3": {
            "label": "Skuffe"
          }
        },
        "mobile_logo_position": {
          "label": "Placering af logo på mobiltelefon",
          "options__1": {
            "label": "Centreret"
          },
          "options__2": {
            "label": "Venstre"
          }
        },
        "logo_help": {
          "content": "Rediger dit logo i [temaindstillinger](/editor?context=theme&category=logo)"
        },
        "sticky_header_type": {
          "label": "Fastgjort sidehoved",
          "options__1": {
            "label": "Ingen"
          },
          "options__2": {
            "label": "Ved oprulning"
          },
          "options__3": {
            "label": "Altid"
          },
          "options__4": {
            "label": "Altid, reducer størrelsen på logo"
          }
        },
        "enable_country_selector": {
          "label": "Lande-/områdevælger",
          "info": "[Adminstrer lande/områder](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Sprogvælger",
          "info": "[Administrer sprog](/admin/settings/languages)"
        },
        "header__1": {
          "content": "Farve"
        },
        "menu_color_scheme": {
          "label": "Farveskema for menu"
        },
        "enable_customer_avatar": {
          "label": "Kundekontos avatar",
          "info": "Kun synlig, når kunder er logget ind med Shop. [Administrer kundekonti](/admin/settings/customer_accounts)"
        },
        "header__utilities": {
          "content": "Forsyning"
        }
      }
    },
    "image-banner": {
      "name": "Billedbanner",
      "settings": {
        "image": {
          "label": "Billede 1"
        },
        "image_2": {
          "label": "Billede 2"
        },
        "stack_images_on_mobile": {
          "label": "Stabl billeder"
        },
        "show_text_box": {
          "label": "Beholder"
        },
        "image_overlay_opacity": {
          "label": "Overlejringens uigennemsigtighed"
        },
        "show_text_below": {
          "label": "Beholder"
        },
        "image_height": {
          "label": "Højde",
          "options__1": {
            "label": "Tilpas til første billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Mellem"
          },
          "options__4": {
            "label": "Stor"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Øverst til venstre"
          },
          "options__2": {
            "label": "Øverst i midten"
          },
          "options__3": {
            "label": "Øverst til højre"
          },
          "options__4": {
            "label": "Midt på til venstre"
          },
          "options__5": {
            "label": "Midt på centreret"
          },
          "options__6": {
            "label": "Midt på til højre"
          },
          "options__7": {
            "label": "Nederst til venstre"
          },
          "options__8": {
            "label": "Nederst i midten"
          },
          "options__9": {
            "label": "Nederst til højre"
          },
          "label": "Placering"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering"
        },
        "mobile": {
          "content": "Mobillayout"
        },
        "content": {
          "content": "Indhold"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Billedbanner"
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "Giv kunder oplysninger om bannerbillederne eller indholdet i skabelonen."
            },
            "text_style": {
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "options__3": {
                "label": "Store bogstaver"
              },
              "label": "Stil"
            }
          }
        },
        "buttons": {
          "name": "Knapper",
          "settings": {
            "button_label_1": {
              "label": "Etiket",
              "info": "Lad stå tom for at skjule",
              "default": "Knaptekst"
            },
            "button_link_1": {
              "label": "Link"
            },
            "button_style_secondary_1": {
              "label": "Rammetypografi"
            },
            "button_label_2": {
              "label": "Etiket",
              "info": "Lad stå tom for at skjule",
              "default": "Knaptekst"
            },
            "button_link_2": {
              "label": "Link"
            },
            "button_style_secondary_2": {
              "label": "Rammetypografi"
            },
            "header_1": {
              "content": "Knap 1"
            },
            "header_2": {
              "content": "Knap 2"
            }
          }
        }
      },
      "presets": {
        "name": "Billedbanner"
      }
    },
    "image-with-text": {
      "name": "Billede med tekst",
      "settings": {
        "image": {
          "label": "Billede"
        },
        "height": {
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Mellem"
          },
          "label": "Højde",
          "options__4": {
            "label": "Stor"
          }
        },
        "layout": {
          "options__1": {
            "label": "Billede først"
          },
          "options__2": {
            "label": "Andet billede"
          },
          "label": "Placering"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Lille"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Bredde"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering",
          "options__2": {
            "label": "Centreret"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "I midten"
          },
          "options__3": {
            "label": "Bund"
          },
          "label": "Placering"
        },
        "content_layout": {
          "options__1": {
            "label": "Ingen overlapning"
          },
          "options__2": {
            "label": "Overlapning"
          },
          "label": "Layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af mobil",
          "options__2": {
            "label": "Centreret"
          }
        },
        "header": {
          "content": "Indhold"
        },
        "header_colors": {
          "content": "Farver"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Billede med tekst"
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "<p>Kombiner tekst med et billede for at fokusere på dit valgte produkt, din valgte kollektion eller dit valgte blogopslag. Tilføj oplysninger om tilgængelighed, stil og eventuelt også en anmeldelse.</p>"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              }
            }
          }
        },
        "button": {
          "name": "Knap",
          "settings": {
            "button_label": {
              "label": "Etiket",
              "info": "Lad stå tom for at skjule",
              "default": "Knaptekst"
            },
            "button_link": {
              "label": "Link"
            },
            "outline_button": {
              "label": "Rammetypografi"
            }
          }
        },
        "caption": {
          "name": "Billedtekst",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "Tilføj et slogan"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Underoverskrift"
              },
              "options__2": {
                "label": "Store bogstaver"
              }
            },
            "caption_size": {
              "label": "Størrelse",
              "options__1": {
                "label": "Lille"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Stor"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Billede med tekst"
      }
    },
    "main-article": {
      "name": "Blogopslag",
      "blocks": {
        "featured_image": {
          "name": "Udvalgt billede",
          "settings": {
            "image_height": {
              "label": "Billedets højde",
              "options__1": {
                "label": "Tilpas til billede"
              },
              "options__2": {
                "label": "Lille"
              },
              "options__3": {
                "label": "Medium"
              },
              "options__4": {
                "label": "Stor"
              }
            }
          }
        },
        "title": {
          "name": "Titel",
          "settings": {
            "blog_show_date": {
              "label": "Dato"
            },
            "blog_show_author": {
              "label": "Forfatter"
            }
          }
        },
        "content": {
          "name": "Indhold"
        },
        "share": {
          "name": "Del",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "Del"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blogopslag",
      "settings": {
        "show_image": {
          "label": "Udvalgt billede"
        },
        "show_date": {
          "label": "Dato"
        },
        "show_author": {
          "label": "Forfatter"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Gitter"
          },
          "options__2": {
            "label": "Kollage"
          }
        },
        "image_height": {
          "label": "Billedets højde",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Stor"
          }
        }
      }
    },
    "main-cart-footer": {
      "name": "Subtotal",
      "blocks": {
        "subtotal": {
          "name": "Subtotal"
        },
        "buttons": {
          "name": "Betalingsknap"
        }
      }
    },
    "main-cart-items": {
      "name": "Varer"
    },
    "main-collection-banner": {
      "name": "Kollektionsbanner",
      "settings": {
        "paragraph": {
          "content": "Kollektionsoplysninger [administreres i din administrator](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Beskrivelse"
        },
        "show_collection_image": {
          "label": "Billede"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Produktgitter",
      "settings": {
        "products_per_page": {
          "label": "Produkter pr. side"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundær baggrund, når der peges"
        },
        "show_vendor": {
          "label": "Forhandler"
        },
        "enable_tags": {
          "label": "Filtre",
          "info": "Tilpas filtre med [Search & Discovery-appen](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_filtering": {
          "label": "Filtre",
          "info": "Tilpas filtre med [Search & Discovery-appen](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Sortering"
        },
        "header__1": {
          "content": "Filtrering og sortering"
        },
        "header__3": {
          "content": "Produktkort"
        },
        "show_rating": {
          "label": "Produktbedømmelse",
          "info": "Der kræves en app til produktbedømmelser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "columns_mobile": {
          "label": "Mobilkolonner",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "filter_type": {
          "label": "Filterlayout",
          "options__1": {
            "label": "Vandret"
          },
          "options__2": {
            "label": "Lodret"
          },
          "options__3": {
            "label": "Skuffe"
          }
        },
        "quick_add": {
          "label": "Hurtig tilføjelse",
          "options": {
            "option_1": "Ingen",
            "option_2": "Standard",
            "option_3": "Masse"
          }
        }
      }
    },
    "main-list-collections": {
      "name": "Siden Kollektionsliste",
      "settings": {
        "title": {
          "label": "Overskrift",
          "default": "Kollektioner"
        },
        "sort": {
          "label": "Sortér kollektioner",
          "options__1": {
            "label": "Alfabetisk, A-Å"
          },
          "options__2": {
            "label": "Alfabetisk, Å-A"
          },
          "options__3": {
            "label": "Dato, nyere til ældre"
          },
          "options__4": {
            "label": "Dato, ældre til nyere"
          },
          "options__5": {
            "label": "Produktantal, høj til lav"
          },
          "options__6": {
            "label": "Produktantal, lav til høj"
          }
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          }
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Mobilkolonner",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "main-page": {
      "name": "Side"
    },
    "main-password-footer": {
      "name": "Sidefod på adgangskodeside"
    },
    "main-password-header": {
      "name": "Sidehoved på adgangskodesiden",
      "settings": {
        "logo_help": {
          "content": "Rediger dit logo i [temaindstillinger](/editor?context=theme&category=logo)"
        }
      }
    },
    "main-product": {
      "name": "Produktoplysninger",
      "blocks": {
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekstfarve",
              "default": "Tekstblok"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Pris"
        },
        "quantity_selector": {
          "name": "Antalsvælger"
        },
        "variant_picker": {
          "name": "Variantvælger",
          "settings": {
            "picker_type": {
              "label": "Stil",
              "options__1": {
                "label": "Rullemenu"
              },
              "options__2": {
                "label": "Etiketter"
              }
            },
            "swatch_shape": {
              "label": "Prøve",
              "info": "Få mere at vide om [prøver](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) på produktmuligheder",
              "options__1": {
                "label": "Cirkel"
              },
              "options__2": {
                "label": "Kvadrat"
              },
              "options__3": {
                "label": "Ingen"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Køb-knapper",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynamiske betalingsknapper",
              "info": "Kunderne vil se deres foretrukne betalingsmetode. [Få mere at vide](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            },
            "show_gift_card_recipient": {
              "label": " Muligheder for afsendelse af gavekort",
              "info": "Kunder kan tilføje en personlig besked og planlægge afsendelsesdatoen. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"
            }
          }
        },
        "pickup_availability": {
          "name": "Mulighed for afhentning"
        },
        "description": {
          "name": "Beskrivelse"
        },
        "share": {
          "name": "Del",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "Del"
            }
          }
        },
        "collapsible_tab": {
          "name": "Række, der kan skjules",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Række, der kan skjules"
            },
            "content": {
              "label": "Rækkeindhold"
            },
            "page": {
              "label": "Rækkeindhold fra side"
            },
            "icon": {
              "label": "Ikon",
              "options__1": {
                "label": "Ingen"
              },
              "options__2": {
                "label": "Æble"
              },
              "options__3": {
                "label": "Banan"
              },
              "options__4": {
                "label": "Flaske"
              },
              "options__5": {
                "label": "Æske"
              },
              "options__6": {
                "label": "Gulerod"
              },
              "options__7": {
                "label": "Chatboble"
              },
              "options__8": {
                "label": "Flueben"
              },
              "options__9": {
                "label": "Clipboard"
              },
              "options__10": {
                "label": "Mejeri"
              },
              "options__11": {
                "label": "Laktosefri"
              },
              "options__12": {
                "label": "Tørrer"
              },
              "options__13": {
                "label": "Øje"
              },
              "options__14": {
                "label": "Ild"
              },
              "options__15": {
                "label": "Glutenfri"
              },
              "options__16": {
                "label": "Hjerte"
              },
              "options__17": {
                "label": "Jern"
              },
              "options__18": {
                "label": "Blad"
              },
              "options__19": {
                "label": "Læder"
              },
              "options__20": {
                "label": "Lyn"
              },
              "options__21": {
                "label": "Læbestift"
              },
              "options__22": {
                "label": "Lås"
              },
              "options__23": {
                "label": "Kortnål"
              },
              "options__24": {
                "label": "Nøddefri"
              },
              "options__25": {
                "label": "Bukser"
              },
              "options__26": {
                "label": "Poteaftryk"
              },
              "options__27": {
                "label": "Peber"
              },
              "options__28": {
                "label": "Parfume"
              },
              "options__29": {
                "label": "Fly"
              },
              "options__30": {
                "label": "Plante"
              },
              "options__31": {
                "label": "Prismærke"
              },
              "options__32": {
                "label": "Spørgsmålstegn"
              },
              "options__33": {
                "label": "Genanvendelse"
              },
              "options__34": {
                "label": "Returnering"
              },
              "options__35": {
                "label": "Lineal"
              },
              "options__36": {
                "label": "Serveringsfad"
              },
              "options__37": {
                "label": "Skjorte"
              },
              "options__38": {
                "label": "Sko"
              },
              "options__39": {
                "label": "Silhuet"
              },
              "options__40": {
                "label": "Snefnug"
              },
              "options__41": {
                "label": "Stjerne"
              },
              "options__42": {
                "label": "Stopur"
              },
              "options__43": {
                "label": "Lastbil"
              },
              "options__44": {
                "label": "Vask"
              }
            }
          }
        },
        "popup": {
          "name": "Pop-op",
          "settings": {
            "link_label": {
              "label": "Navn på link",
              "default": "Pop op-linktekst"
            },
            "page": {
              "label": "Side"
            }
          }
        },
        "rating": {
          "name": "Produktvurdering",
          "settings": {
            "paragraph": {
              "content": "Der kræves en app til produktbedømmelser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Supplerende produkter",
          "settings": {
            "paragraph": {
              "content": "Administrer supplerende produkter i [Search & Discovery-appen](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Overskrift",
              "default": "Kan kombineres med"
            },
            "make_collapsible_row": {
              "label": "Række, der kan skjules"
            },
            "icon": {
              "info": "Vises, når rækken, der kan skjules, er valgt."
            },
            "product_list_limit": {
              "label": "Produktantal"
            },
            "products_per_page": {
              "label": "Produkter pr. side"
            },
            "pagination_style": {
              "label": "Sideinddeling",
              "options": {
                "option_1": "Prikker",
                "option_2": "Tæller",
                "option_3": "Tal"
              }
            },
            "product_card": {
              "heading": "Produktkort"
            },
            "image_ratio": {
              "label": "Billedforhold",
              "options": {
                "option_1": "Stående",
                "option_2": "Kvadrat"
              }
            },
            "enable_quick_add": {
              "label": "Tilføj hurtigt"
            }
          }
        },
        "icon_with_text": {
          "name": "Ikon med tekst",
          "settings": {
            "layout": {
              "label": "Layout",
              "options__1": {
                "label": "Vandret"
              },
              "options__2": {
                "label": "Lodret"
              }
            },
            "heading": {
              "info": "Lad stå tomt for at skjule denne parring"
            },
            "icon_1": {
              "label": "Ikon"
            },
            "image_1": {
              "label": "Billede"
            },
            "heading_1": {
              "label": "Overskrift",
              "default": "Overskrift"
            },
            "icon_2": {
              "label": "Ikon"
            },
            "image_2": {
              "label": "Billede"
            },
            "heading_2": {
              "label": "Overskrift",
              "default": "Overskrift"
            },
            "icon_3": {
              "label": "Ikon"
            },
            "image_3": {
              "label": "Billede"
            },
            "heading_3": {
              "label": "Overskrift",
              "default": "Overskrift"
            },
            "pairing_1": {
              "label": "Parring 1",
              "info": "Vælg et ikon, eller tilføj et billede for hver parring"
            },
            "pairing_2": {
              "label": "Parring 2"
            },
            "pairing_3": {
              "label": "Parring 3"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Underoverskrift"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        },
        "inventory": {
          "name": "Lagerstatus",
          "settings": {
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Underoverskrift"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            },
            "inventory_threshold": {
              "label": "Lav grænse for lagerbeholdning"
            },
            "show_inventory_quantity": {
              "label": "Lagerantal"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Medie"
        },
        "enable_video_looping": {
          "label": "Videoloop"
        },
        "enable_sticky_info": {
          "label": "Fastgjort indhold"
        },
        "hide_variants": {
          "label": "Skjul andre variantmedier, når der vælges en"
        },
        "gallery_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Stablet"
          },
          "options__2": {
            "label": "2 kolonner"
          },
          "options__3": {
            "label": "Miniaturer"
          },
          "options__4": {
            "label": "Miniaturekarussel"
          }
        },
        "media_size": {
          "label": "Bredde",
          "options__1": {
            "label": "Lille"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Stor"
          }
        },
        "mobile_thumbnails": {
          "label": "Mobillayout",
          "options__1": {
            "label": "2 kolonner"
          },
          "options__2": {
            "label": "Vis miniaturer"
          },
          "options__3": {
            "label": "Skjul miniaturer"
          }
        },
        "media_position": {
          "label": "Placering",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Højre"
          }
        },
        "image_zoom": {
          "label": "Zoom",
          "options__1": {
            "label": "Åbn lightbox"
          },
          "options__2": {
            "label": "Klik og hold musen over"
          },
          "options__3": {
            "label": "Ingen zoom"
          }
        },
        "constrain_to_viewport": {
          "label": "Begræns til skærmhøjden"
        },
        "media_fit": {
          "label": "Tilpasning",
          "options__1": {
            "label": "Oprindelig"
          },
          "options__2": {
            "label": "Udfyld"
          }
        }
      }
    },
    "main-search": {
      "name": "Søgeresultater",
      "settings": {
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundær baggrund, når der peges"
        },
        "show_vendor": {
          "label": "Forhandler"
        },
        "header__1": {
          "content": "Produktkort"
        },
        "header__2": {
          "content": "Blogkort"
        },
        "article_show_date": {
          "label": "Dato"
        },
        "article_show_author": {
          "label": "Forfatter"
        },
        "show_rating": {
          "label": "Produktbedømmelse",
          "info": "Der kræves en app til produktbedømmelser. [Få mere at vide](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "columns_mobile": {
          "label": "Mobilkolonner",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Flere kolonner",
      "settings": {
        "title": {
          "label": "Overskrift",
          "default": "Flere kolonner"
        },
        "image_width": {
          "label": "Bredde",
          "options__1": {
            "label": "En tredjedel af kolonnens bredde"
          },
          "options__2": {
            "label": "Halvdelen af kolonnens bredde"
          },
          "options__3": {
            "label": "Hele kolonnens bredde"
          }
        },
        "image_ratio": {
          "label": "Forhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          },
          "options__4": {
            "label": "Cirkel"
          }
        },
        "column_alignment": {
          "label": "Kolonnejustering",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          }
        },
        "background_style": {
          "label": "Sekundær baggrund",
          "options__1": {
            "label": "Ingen"
          },
          "options__2": {
            "label": "Vis som kolonnebaggrund"
          }
        },
        "button_label": {
          "label": "Etiket",
          "default": "Knaptekst",
          "info": "Lad stå tom for at skjule"
        },
        "button_link": {
          "label": "Link"
        },
        "swipe_on_mobile": {
          "label": "Karrusel"
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "header_mobile": {
          "content": "Mobillayout"
        },
        "columns_mobile": {
          "label": "Kolonner",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_text": {
          "content": "Overskrift"
        },
        "header_image": {
          "content": "Billede"
        },
        "header_layout": {
          "content": "Layout"
        },
        "header_button": {
          "content": "Knap"
        }
      },
      "blocks": {
        "column": {
          "name": "Kolonne",
          "settings": {
            "image": {
              "label": "Billede"
            },
            "title": {
              "label": "Overskrift",
              "default": "Kolonne"
            },
            "text": {
              "label": "Beskrivelse",
              "default": "<p>Kombiner tekst med et billede for at fokusere på dit valgte produkt, din valgte kollektion eller dit valgte blogopslag. Tilføj oplysninger om tilgængelighed, stil og eventuelt også en anmeldelse.</p>"
            },
            "link_label": {
              "label": "Navn på link",
              "info": "Lad stå tom for at skjule"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Flere kolonner"
      }
    },
    "newsletter": {
      "name": "Tilmelding med mail",
      "settings": {
        "full_width": {
          "label": "Fuld bredde"
        },
        "paragraph": {
          "content": "Tilføjelse af tilmeldinger [kundeprofiler](https://help.shopify.com/manual/customers/manage-customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Tilmeld dig vores mails"
            }
          }
        },
        "paragraph": {
          "name": "Tekst",
          "settings": {
            "paragraph": {
              "label": "Tekst",
              "default": "<p>Vær blandt de første til at få besked om nye kollektioner og eksklusive tilbud.</p>"
            }
          }
        },
        "email_form": {
          "name": "Mailformular"
        }
      },
      "presets": {
        "name": "Tilmelding med mail"
      }
    },
    "page": {
      "name": "Side",
      "settings": {
        "page": {
          "label": "Side"
        }
      },
      "presets": {
        "name": "Side"
      }
    },
    "rich-text": {
      "name": "RTF",
      "settings": {
        "full_width": {
          "label": "Fuld bredde"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Indholdets placering"
        },
        "content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Indholdsjustering"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Fortæl om dit brand"
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "<p>Del oplysninger om dit brand med dine kunder. Beskriv et produkt, del meddelelser, eller byd velkommen til din butik.</p>"
            }
          }
        },
        "buttons": {
          "name": "Knapper",
          "settings": {
            "button_label_1": {
              "label": "Etiket",
              "info": "Lad stå tom for at skjule",
              "default": "Knaptekst"
            },
            "button_link_1": {
              "label": "Link"
            },
            "button_style_secondary_1": {
              "label": "Rammetypografi"
            },
            "button_label_2": {
              "label": "Etiket",
              "info": "Lad etiket være tom for at skjule"
            },
            "button_link_2": {
              "label": "Link"
            },
            "button_style_secondary_2": {
              "label": "Rammetypografi"
            },
            "header_button1": {
              "content": "Knap 1"
            },
            "header_button2": {
              "content": "Knap 2"
            }
          }
        },
        "caption": {
          "name": "Billedtekst",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "Tilføj et slogan"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Underoverskrift"
              },
              "options__2": {
                "label": "Store bogstaver"
              }
            },
            "caption_size": {
              "label": "Størrelse",
              "options__1": {
                "label": "Lille"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Stor"
              }
            }
          }
        }
      },
      "presets": {
        "name": "RTF"
      }
    },
    "apps": {
      "name": "Apps",
      "settings": {
        "include_margins": {
          "label": "Gør afsnitsmargener til det samme som tema"
        }
      },
      "presets": {
        "name": "Apps"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Overskrift",
          "default": "Video"
        },
        "cover_image": {
          "label": "Coverbillede"
        },
        "video_url": {
          "label": "Webadresse",
          "info": "Brug en YouTube- eller Vimeo-webadresse"
        },
        "description": {
          "label": "Alternativ tekst til video",
          "info": "Beskriv videoen for brugere med skærmlæser"
        },
        "image_padding": {
          "label": "Tilføj billedmargen",
          "info": "Vælg billedmargen, hvis du ikke vil have, at dit coverbillede bliver beskåret."
        },
        "full_width": {
          "label": "Fuld bredde"
        },
        "video": {
          "label": "Video"
        },
        "enable_video_looping": {
          "label": "Videoloop"
        },
        "header__1": {
          "content": "Video hostet af Shopify"
        },
        "header__2": {
          "content": "Eller integrer video fra webadresse"
        },
        "header__3": {
          "content": "Layout"
        },
        "paragraph": {
          "content": "Vises, når der ikke er valgt en video, der er hostet af Shopify"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Fremhævet produkt",
      "blocks": {
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekst",
              "default": "Tekstblok"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Pris"
        },
        "quantity_selector": {
          "name": "Antalsvælger"
        },
        "variant_picker": {
          "name": "Variantvælger",
          "settings": {
            "picker_type": {
              "label": "Stil",
              "options__1": {
                "label": "Rullemenu"
              },
              "options__2": {
                "label": "Etiketter"
              }
            },
            "swatch_shape": {
              "label": "Prøve",
              "info": "Få mere at vide om [prøver](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) på produktmuligheder",
              "options__1": {
                "label": "Cirkel"
              },
              "options__2": {
                "label": "Kvadrat"
              },
              "options__3": {
                "label": "Ingen"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Køb-knapper",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynamiske betalingsknapper",
              "info": "Kunderne vil se deres foretrukne betalingsmetode. [Få mere at vide](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Beskrivelse"
        },
        "share": {
          "name": "Del",
          "settings": {
            "featured_image_info": {
              "content": "Hvis du inkluderer et link i opslag på sociale medier, vil sidens udvalgte billede blive vist som billedeksempel. [Få mere at vide](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Der er inkluderet en butikstitel og -beskrivelse med billedeksemplet. [Få mere at vide](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Tekst",
              "default": "Del"
            }
          }
        },
        "rating": {
          "name": "Produktbedømmelser",
          "settings": {
            "paragraph": {
              "content": "Der kræves en app til produktbedømmelser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Teksttypografi",
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Underoverskrift"
              },
              "options__3": {
                "label": "Store bogstaver"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Produkt"
        },
        "secondary_background": {
          "label": "Sekundær baggrund"
        },
        "header": {
          "content": "Medie"
        },
        "enable_video_looping": {
          "label": "Videoloop"
        },
        "hide_variants": {
          "label": "Skjul medier for ikke-valgte varianter på computer"
        },
        "media_position": {
          "label": "Placering",
          "info": "Placeringen optimeres automatisk til mobil.",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Højre"
          }
        }
      },
      "presets": {
        "name": "Fremhævet produkt"
      }
    },
    "email-signup-banner": {
      "name": "Banner for tilmelding med mail",
      "settings": {
        "paragraph": {
          "content": "Tilføjelse af tilmeldinger [kundeprofiler](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "image": {
          "label": "Baggrundsbillede"
        },
        "show_background_image": {
          "label": "Vis baggrundsbillede"
        },
        "show_text_box": {
          "label": "Beholder"
        },
        "image_overlay_opacity": {
          "label": "Overlejringens uigennemsigtighed"
        },
        "show_text_below": {
          "label": "Stabl tekst under billede"
        },
        "image_height": {
          "label": "Højde",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Stor"
          }
        },
        "desktop_content_position": {
          "options__4": {
            "label": "Midt på til venstre"
          },
          "options__5": {
            "label": "Midt på centreret"
          },
          "options__6": {
            "label": "Midt på til højre"
          },
          "options__7": {
            "label": "Nederst til venstre"
          },
          "options__8": {
            "label": "Nederst i midten"
          },
          "options__9": {
            "label": "Nederst til højre"
          },
          "options__1": {
            "label": "Øverst til venstre"
          },
          "options__2": {
            "label": "Øverst i midten"
          },
          "options__3": {
            "label": "Øverst til højre"
          },
          "label": "Placering"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering"
        },
        "header": {
          "content": "Mobillayout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering"
        },
        "color_scheme": {
          "info": "Synlig, når objektbeholderen vises."
        },
        "content_header": {
          "content": "Indhold"
        }
      },
      "blocks": {
        "heading": {
          "name": "Overskrift",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Åbner snart"
            }
          }
        },
        "paragraph": {
          "name": "Tekst",
          "settings": {
            "paragraph": {
              "label": "Tekst",
              "default": "<p>Vær blandt de første til at få besked ved lancering.</p>"
            },
            "text_style": {
              "options__1": {
                "label": "Brødtekst"
              },
              "options__2": {
                "label": "Undertekst"
              },
              "label": "Stil"
            }
          }
        },
        "email_form": {
          "name": "Mailformular"
        }
      },
      "presets": {
        "name": "Banner for tilmelding med mail"
      }
    },
    "slideshow": {
      "name": "Diasshow",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Fuld bredde"
          },
          "options__2": {
            "label": "Side"
          }
        },
        "slide_height": {
          "label": "Højde",
          "options__1": {
            "label": "Tilpas til første side"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Stor"
          }
        },
        "slider_visual": {
          "label": "Sideinddeling",
          "options__1": {
            "label": "Tæller"
          },
          "options__2": {
            "label": "Prikker"
          },
          "options__3": {
            "label": "Tal"
          }
        },
        "auto_rotate": {
          "label": "Roter slides automatisk"
        },
        "change_slides_speed": {
          "label": "Skift slide hver"
        },
        "mobile": {
          "content": "Mobillayout"
        },
        "show_text_below": {
          "label": "Stabl tekst under billede"
        },
        "accessibility": {
          "content": "Tilgængelighed",
          "label": "Beskrivelse af diasshow",
          "info": "Beskriv diasshowet for brugere med skærmlæser",
          "default": "Diasshow om vores brand"
        }
      },
      "blocks": {
        "slide": {
          "name": "Slide",
          "settings": {
            "image": {
              "label": "Billede"
            },
            "heading": {
              "label": "Overskrift",
              "default": "Billeddias"
            },
            "subheading": {
              "label": "Underoverskrift",
              "default": "Fortæl dit brands historie gennem billeder"
            },
            "button_label": {
              "label": "Etiket",
              "info": "Lad stå tom for at skjule",
              "default": "Knaptekst"
            },
            "link": {
              "label": "Link"
            },
            "secondary_style": {
              "label": "Rammetypografi"
            },
            "box_align": {
              "label": "Indholdets placering",
              "options__1": {
                "label": "Øverst til venstre"
              },
              "options__2": {
                "label": "Øverst i midten"
              },
              "options__3": {
                "label": "Øverst til højre"
              },
              "options__4": {
                "label": "Midt på til venstre"
              },
              "options__5": {
                "label": "Midt på centreret"
              },
              "options__6": {
                "label": "Midt på til højre"
              },
              "options__7": {
                "label": "Nederst til venstre"
              },
              "options__8": {
                "label": "Nederst i midten"
              },
              "options__9": {
                "label": "Nederst til højre"
              }
            },
            "show_text_box": {
              "label": "Beholder"
            },
            "text_alignment": {
              "label": "Indholdets justering",
              "option_1": {
                "label": "Venstre"
              },
              "option_2": {
                "label": "Centreret"
              },
              "option_3": {
                "label": "Højre"
              }
            },
            "image_overlay_opacity": {
              "label": "Overlejringens uigennemsigtighed"
            },
            "text_alignment_mobile": {
              "label": "Justering af indhold på mobil",
              "options__1": {
                "label": "Venstre"
              },
              "options__2": {
                "label": "Centreret"
              },
              "options__3": {
                "label": "Højre"
              }
            },
            "header_button": {
              "content": "Knap"
            },
            "header_layout": {
              "content": "Layout"
            },
            "header_text": {
              "content": "Tekst"
            },
            "header_colors": {
              "content": "Farver"
            }
          }
        }
      },
      "presets": {
        "name": "Diasshow"
      }
    },
    "collapsible_content": {
      "name": "Indhold, der kan skjules",
      "settings": {
        "caption": {
          "label": "Billedtekst"
        },
        "heading": {
          "label": "Overskrift",
          "default": "Indhold, der kan skjules"
        },
        "heading_alignment": {
          "label": "Justering af overskrift",
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          }
        },
        "layout": {
          "label": "Beholder",
          "options__1": {
            "label": "Ingen beholder"
          },
          "options__2": {
            "label": "Objektbeholder til række"
          },
          "options__3": {
            "label": "Objektbeholder til afsnit"
          }
        },
        "open_first_collapsible_row": {
          "label": "Åbn første række"
        },
        "header": {
          "content": "Billede"
        },
        "image": {
          "label": "Billede"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Stor"
          }
        },
        "desktop_layout": {
          "label": "Placering",
          "options__1": {
            "label": "Billede først"
          },
          "options__2": {
            "label": "Billede efterfølgende"
          }
        },
        "container_color_scheme": {
          "label": "Objektbeholder til farveskema"
        },
        "layout_header": {
          "content": "Layout"
        },
        "section_color_scheme": {
          "label": "Sektionens farveskema"
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Række, der kan skjules",
          "settings": {
            "heading": {
              "label": "Overskrift",
              "default": "Række, der kan skjules"
            },
            "row_content": {
              "label": "Rækkeindhold"
            },
            "page": {
              "label": "Rækkeindhold fra side"
            },
            "icon": {
              "label": "Ikon",
              "options__1": {
                "label": "Ingen"
              },
              "options__2": {
                "label": "Æble"
              },
              "options__3": {
                "label": "Banan"
              },
              "options__4": {
                "label": "Flaske"
              },
              "options__5": {
                "label": "Æske"
              },
              "options__6": {
                "label": "Gulerod"
              },
              "options__7": {
                "label": "Chatboble"
              },
              "options__8": {
                "label": "Flueben"
              },
              "options__9": {
                "label": "Clipboard"
              },
              "options__10": {
                "label": "Mejeri"
              },
              "options__11": {
                "label": "Laktosefri"
              },
              "options__12": {
                "label": "Tørrer"
              },
              "options__13": {
                "label": "Øje"
              },
              "options__14": {
                "label": "Ild"
              },
              "options__15": {
                "label": "Glutenfri"
              },
              "options__16": {
                "label": "Hjerte"
              },
              "options__17": {
                "label": "Jern"
              },
              "options__18": {
                "label": "Blad"
              },
              "options__19": {
                "label": "Læder"
              },
              "options__20": {
                "label": "Lyn"
              },
              "options__21": {
                "label": "Læbestift"
              },
              "options__22": {
                "label": "Lås"
              },
              "options__23": {
                "label": "Kortnål"
              },
              "options__24": {
                "label": "Nøddefri"
              },
              "options__25": {
                "label": "Bukser"
              },
              "options__26": {
                "label": "Poteaftryk"
              },
              "options__27": {
                "label": "Peber"
              },
              "options__28": {
                "label": "Parfume"
              },
              "options__29": {
                "label": "Fly"
              },
              "options__30": {
                "label": "Plante"
              },
              "options__31": {
                "label": "Prismærke"
              },
              "options__32": {
                "label": "Spørgsmålstegn"
              },
              "options__33": {
                "label": "Genanvendelse"
              },
              "options__34": {
                "label": "Returnering"
              },
              "options__35": {
                "label": "Lineal"
              },
              "options__36": {
                "label": "Serveringsfad"
              },
              "options__37": {
                "label": "Skjorte"
              },
              "options__38": {
                "label": "Sko"
              },
              "options__39": {
                "label": "Silhuet"
              },
              "options__40": {
                "label": "Snefnug"
              },
              "options__41": {
                "label": "Stjerne"
              },
              "options__42": {
                "label": "Stopur"
              },
              "options__43": {
                "label": "Lastbil"
              },
              "options__44": {
                "label": "Vask"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Indhold, der kan skjules"
      }
    },
    "main-account": {
      "name": "Konto"
    },
    "main-activate-account": {
      "name": "Aktivering af konto"
    },
    "main-addresses": {
      "name": "Adresser"
    },
    "main-login": {
      "name": "Login",
      "shop_login_button": {
        "enable": "Aktiver log ind med shop"
      }
    },
    "main-order": {
      "name": "Ordre"
    },
    "main-register": {
      "name": "Registrering"
    },
    "main-reset-password": {
      "name": "Nulstilling af adgangskode"
    },
    "related-products": {
      "name": "Relaterede produkter",
      "settings": {
        "heading": {
          "label": "Overskrift"
        },
        "products_to_show": {
          "label": "Produktantal"
        },
        "columns_desktop": {
          "label": "Kolonner"
        },
        "paragraph__1": {
          "content": "Relaterede produkter kan administreres i [Search & Discovery-appen](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)",
          "default": "Du vil muligvis også synes om"
        },
        "header__2": {
          "content": "Produktkort"
        },
        "image_ratio": {
          "label": "Billedforhold",
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Stående"
          },
          "options__3": {
            "label": "Kvadrat"
          }
        },
        "show_secondary_image": {
          "label": "Vis sekundært billede, når der peges"
        },
        "show_vendor": {
          "label": "Forhandler"
        },
        "show_rating": {
          "label": "Produktbedømmelse",
          "info": "Der kræves en app til produktbedømmelser. [Få mere at vide](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"
        },
        "columns_mobile": {
          "label": "Mobilkolonner",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multirow": {
      "name": "Flere rækker",
      "settings": {
        "image": {
          "label": "Billede"
        },
        "image_height": {
          "options__1": {
            "label": "Tilpas til billede"
          },
          "options__2": {
            "label": "Lille"
          },
          "options__3": {
            "label": "Mellem"
          },
          "options__4": {
            "label": "Stor"
          },
          "label": "Højde"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Lille"
          },
          "options__2": {
            "label": "Mellem"
          },
          "options__3": {
            "label": "Stor"
          },
          "label": "Bredde"
        },
        "text_style": {
          "options__1": {
            "label": "Brødtekst"
          },
          "options__2": {
            "label": "Underoverskrift"
          },
          "label": "Teksttypografi"
        },
        "button_style": {
          "options__1": {
            "label": "Udfyldt knap"
          },
          "options__2": {
            "label": "Rammeknap"
          },
          "label": "Knaptypografi"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "I midten"
          },
          "options__3": {
            "label": "Bund"
          },
          "label": "Placering"
        },
        "image_layout": {
          "options__1": {
            "label": "Skift fra venstre"
          },
          "options__2": {
            "label": "Skift fra højre"
          },
          "options__3": {
            "label": "Venstrejusteret"
          },
          "options__4": {
            "label": "Højrejusteret"
          },
          "label": "Placering"
        },
        "container_color_scheme": {
          "label": "Objektbeholder til farveskema"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Venstre"
          },
          "options__2": {
            "label": "Centreret"
          },
          "options__3": {
            "label": "Højre"
          },
          "label": "Justering af mobil"
        },
        "header": {
          "content": "Billede"
        },
        "header_2": {
          "content": "Indhold"
        },
        "header_3": {
          "content": "Farver"
        }
      },
      "blocks": {
        "row": {
          "name": "Række",
          "settings": {
            "image": {
              "label": "Billede"
            },
            "caption": {
              "label": "Billedtekst",
              "default": "Billedtekst"
            },
            "heading": {
              "label": "Overskrift",
              "default": "Række"
            },
            "text": {
              "label": "Sms",
              "default": "<p>Kombiner tekst med et billede for at fokusere på dit valgte produkt, din valgte kollektion eller dit valgte blogopslag. Tilføj oplysninger om tilgængelighed, stil og eventuelt også en anmeldelse.</p>"
            },
            "button_label": {
              "label": "Knaptekst",
              "default": "Knaptekst",
              "info": "Lad stå tom for at skjule"
            },
            "button_link": {
              "label": "Knaplink"
            }
          }
        }
      },
      "presets": {
        "name": "Flere rækker"
      }
    },
    "quick-order-list": {
      "name": "Hurtig ordreliste",
      "settings": {
        "show_image": {
          "label": "Billeder"
        },
        "show_sku": {
          "label": "SKU'er"
        },
        "variants_per_page": {
          "label": "Varianter pr. side"
        }
      },
      "presets": {
        "name": "Hurtig ordreliste"
      }
    }
  }
}
