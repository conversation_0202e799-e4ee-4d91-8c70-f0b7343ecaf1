/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "global": {
      "settings": {
        "header__border": {
          "content": "Border"
        },
        "header__shadow": {
          "content": "Shadow"
        },
        "blur": {
          "label": "Blur"
        },
        "corner_radius": {
          "label": "Corner radius"
        },
        "horizontal_offset": {
          "label": "Horizontal offset"
        },
        "vertical_offset": {
          "label": "Vertical offset"
        },
        "thickness": {
          "label": "Thickness"
        },
        "opacity": {
          "label": "Opacity"
        },
        "image_padding": {
          "label": "Image padding"
        },
        "text_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Text alignment"
        }
      }
    },
    "cards": {
      "name": "Product cards",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Card"
          },
          "label": "Style"
        }
      }
    },
    "collection_cards": {
      "name": "Collection cards",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Card"
          },
          "label": "Style"
        }
      }
    },
    "blog_cards": {
      "name": "Blog cards",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Card"
          },
          "label": "Style"
        }
      }
    },
    "badges": {
      "name": "Badges",
      "settings": {
        "position": {
          "options__1": {
            "label": "Bottom left"
          },
          "options__2": {
            "label": "Bottom right"
          },
          "options__3": {
            "label": "Top left"
          },
          "options__4": {
            "label": "Top right"
          },
          "label": "Position on cards"
        },
        "sale_badge_color_scheme": {
          "label": "Sale badge color scheme"
        },
        "sold_out_badge_color_scheme": {
          "label": "Sold out badge color scheme"
        }
      }
    },
    "colors": {
      "name": "Colors",
      "settings": {
        "background": {
          "label": "Background"
        },
        "background_gradient": {
          "label": "Background gradient",
          "info": "Background gradient replaces background where possible."
        },
        "text": {
          "label": "Text"
        },
        "button_background": {
          "label": "Solid button background"
        },
        "button_label": {
          "label": "Solid button label"
        },
        "secondary_button_label": {
          "label": "Outline button"
        },
        "shadow": {
          "label": "Shadow"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Width"
        },
        "favicon": {
          "label": "Favicon",
          "info": "Displayed at 32 x 32px"
        }
      }
    },
    "brand_information": {
      "name": "Brand information",
      "settings": {
        "paragraph": {
          "content": "Displays in the footer's brand information block"
        },
        "brand_headline": {
          "label": "Headline"
        },
        "brand_description": {
          "label": "Description"
        },
        "brand_image": {
          "label": "Image"
        },
        "brand_image_width": {
          "label": "Image width"
        }
      }
    },
    "typography": {
      "name": "Typography",
      "settings": {
        "type_header_font": {
          "label": "Font"
        },
        "heading_scale": {
          "label": "Scale"
        },
        "header__1": {
          "content": "Headings"
        },
        "header__2": {
          "content": "Body"
        },
        "type_body_font": {
          "label": "Font"
        },
        "body_scale": {
          "label": "Scale"
        }
      }
    },
    "buttons": {
      "name": "Buttons"
    },
    "variant_pills": {
      "name": "Variant pills",
      "paragraph": "Variant pills are one way of displaying your [product variants](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block)"
    },
    "inputs": {
      "name": "Inputs"
    },
    "content_containers": {
      "name": "Content containers"
    },
    "popups": {
      "name": "Dropdowns and pop-ups",
      "paragraph": "Affects areas like navigation dropdowns, pop-up modals, and cart pop-ups"
    },
    "media": {
      "name": "Media"
    },
    "drawers": {
      "name": "Drawers"
    },
    "animations": {
      "name": "Animations",
      "settings": {
        "animations_reveal_on_scroll": {
          "label": "Reveal sections on scroll"
        },
        "animations_hover_elements": {
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "Vertical lift"
          },
          "options__3": {
            "label": "3D lift"
          },
          "label": "Hover effect",
          "info": "Affects cards and buttons"
        }
      }
    },
    "social-media": {
      "name": "Social media",
      "settings": {
        "header": {
          "content": "Social accounts"
        },
        "social_twitter_link": {
          "label": "X / Twitter",
          "info": "https://x.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "https://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        }
      }
    },
    "search_input": {
      "name": "Search behavior",
      "settings": {
        "predictive_search_enabled": {
          "label": "Search suggestions"
        },
        "predictive_search_show_vendor": {
          "label": "Product vendor",
          "info": "Shown when search suggestions enabled"
        },
        "predictive_search_show_price": {
          "label": "Product price",
          "info": "Shown when search suggestions enabled"
        }
      }
    },
    "currency_format": {
      "name": "Currency format",
      "settings": {
        "paragraph": "Cart and checkout prices always show currency codes",
        "currency_code_enabled": {
          "label": "Currency codes"
        }
      }
    },
    "cart": {
      "name": "Cart",
      "settings": {
        "cart_type": {
          "label": "Type",
          "drawer": {
            "label": "Drawer"
          },
          "page": {
            "label": "Page"
          },
          "notification": {
            "label": "Popup notification"
          }
        },
        "show_vendor": {
          "label": "Vendor"
        },
        "show_cart_note": {
          "label": "Cart note"
        },
        "cart_drawer": {
          "header": "Cart drawer",
          "collection": {
            "label": "Collection",
            "info": "Shown when cart drawer is empty"
          }
        }
      }
    },
    "layout": {
      "name": "Layout",
      "settings": {
        "page_width": {
          "label": "Page width"
        },
        "spacing_sections": {
          "label": "Space between template sections"
        },
        "header__grid": {
          "content": "Grid"
        },
        "paragraph__grid": {
          "content": "Affects areas with multiple columns or rows"
        },
        "spacing_grid_horizontal": {
          "label": "Horizontal space"
        },
        "spacing_grid_vertical": {
          "label": "Vertical space"
        }
      }
    }
  },
  "sections": {
    "all": {
      "animation": {
        "content": "Animations",
        "image_behavior": {
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "Ambient movement"
          },
          "options__3": {
            "label": "Fixed background position"
          },
          "options__4": {
            "label": "Zoom in on scroll"
          },
          "label": "Animation"
        }
      },
      "padding": {
        "section_padding_heading": "Padding",
        "padding_top": "Top",
        "padding_bottom": "Bottom"
      },
      "spacing": "Spacing",
      "colors": {
        "label": "Color scheme",
        "has_cards_info": "To change the card color scheme, update your theme settings."
      },
      "heading_size": {
        "label": "Heading size",
        "options__1": {
          "label": "Small"
        },
        "options__2": {
          "label": "Medium"
        },
        "options__3": {
          "label": "Large"
        },
        "options__4": {
          "label": "Extra large"
        },
        "options__5": {
          "label": "Extra extra large"
        }
      },
      "image_shape": {
        "options__1": {
          "label": "Default"
        },
        "options__2": {
          "label": "Arch"
        },
        "options__3": {
          "label": "Blob"
        },
        "options__4": {
          "label": "Chevron left"
        },
        "options__5": {
          "label": "Chevron right"
        },
        "options__6": {
          "label": "Diamond"
        },
        "options__7": {
          "label": "Parallelogram"
        },
        "options__8": {
          "label": "Round"
        },
        "label": "Image shape"
      }
    },
    "announcement-bar": {
      "name": "Announcement bar",
      "settings": {
        "auto_rotate": {
          "label": "Auto rotate announcements"
        },
        "change_slides_speed": {
          "label": "Change every"
        },
        "heading_utilities": {
         "content": "Utilities"
        },
        "paragraph": {
          "content": "Appear only on large screens"
        },                     
        "show_social": {
          "label": "Social media icons",
          "info": "[Manage social accounts](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Country/region selector",
          "info": "[Manage countries/regions](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Language selector",
          "info": "[Manage languages](/admin/settings/languages)"
        }
      },
      "blocks": {
        "announcement": {
          "name": "Announcement",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Welcome to our store"
            },
            "text_alignment": {
              "label": "Text alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              },
              "options__3": {
                "label": "Right"
              }
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Announcement bar"
      }
    },
    "apps": {
      "name": "Apps",
      "settings": {
        "include_margins": {
          "label": "Make section margins the same as theme"
        }
      },
      "presets": {
        "name": "Apps"
      }
    },
    "collage": {
      "name": "Collage",
      "settings": {
        "heading": {
          "default": "Multimedia collage",
          "label": "Heading"
        },
        "header_layout": {
          "content": "Layout"
        },            
        "desktop_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Large block first"
          },
          "options__2": {
            "label": "Large block last"
          }
        },
        "mobile_layout": {
          "label": "Mobile layout",
          "options__1": {
            "label": "Collage"
          },
          "options__2": {
            "label": "Column"
          }
        },
        "card_styles": {
          "label": "Card style",
          "info": "Manage individual card styles in [theme settings](/editor?context=theme&category=product%20cards)",
          "options__1": {
            "label": "Use individual card styles"
          },
          "options__2": {
            "label": "Style all as product cards"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Image"
            }
          }
        },
        "product": {
          "name": "Product",
          "settings": {
            "product": {
              "label": "Product"
            },
            "secondary_background": {
              "label": "Show secondary background"
            },
            "second_image": {
              "label": "Show second image on hover"
            }
          }
        },
        "collection": {
          "name": "Collection",
          "settings": {
            "collection": {
              "label": "Collection"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Cover image"
            },
            "video_url": {
              "label": "URL",
              "info": "Video plays in a pop-up if the section contains other blocks.",
              "placeholder": "Use a YouTube or Vimeo URL"
            },
            "description": {
              "default": "Describe the video",
              "label": "Video alt text",
              "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"
            }
          }
        }
      },
      "presets": {
        "name": "Collage"
      }
    },
    "collection-list": {
      "name": "Collection list",
      "settings": {
        "title": {
          "default": "Collections",
          "label": "Heading"
        },
         "header_layout": {
        "content": "Layout"
        },        
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "columns_desktop": {
          "label": "Columns"
        },
        "show_view_all": {
          "label": "\"View all\" button",
          "info": "Visible if list has more collections than shown"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Columns",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "swipe_on_mobile": {
          "label": "Carousel"
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Collection",
          "settings": {
            "collection": {
              "label": "Collection"
            }
          }
        }
      },
      "presets": {
        "name": "Collection list"
      }
    },
    "contact-form": {
      "name": "Contact Form",
      "presets": {
        "name": "Contact form"
      },
      "settings": {
        "title": {
          "default": "Contact form",
          "label": "Heading"
        }
      }
    },
    "custom-liquid": {
      "name": "Custom Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Liquid code",
          "info": "Add app snippets or other code to create advanced customizations. [Learn more](https://shopify.dev/docs/api/liquid)"
        }
      },
      "presets": {
        "name": "Custom Liquid"
      }
    },
    "featured-blog": {
      "name": "Blog posts",
      "settings": {
        "heading": {
          "default": "Blog posts",
          "label": "Heading"
        },
        "layout_header": {
        "content": "Layout"
        },
        "text_header": {
        "content": "Text"
        },        
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Post count"
        }, 
        "columns_desktop": {
          "label": "Columns"
        },
        "show_view_all": {
          "label": "\"View all\" button",
          "info": "Visible if blog has more posts than shown"
        },
        "show_image": {
          "label": "Featured image"
        },
        "show_date": {
          "label": "Date"
        },
        "show_author": {
          "label": "Author"
        }
      },
      "presets": {
        "name": "Blog posts"
      }
    },
    "featured-collection": {
      "name": "Featured collection",
      "settings": {
        "header_text": {
        "content": "Text"
        },
        "title": {
          "label": "Heading",
          "default": "Featured collection"
        },
        "description": {
          "label": "Description"
        },
        "show_description": {
          "label": "Show collection description from admin"
        },
        "description_style": {
          "label": "Description style",
          "options__1": {
            "label": "Body"
          },
          "options__2": {
            "label": "Subtitle"
          },
          "options__3": {
            "label": "Uppercase"
          }
        },
        "header_collection": {
          "content": "Collection layout"
        },
        "header_mobile": {
        "content": "Mobile layout"
        },                 
        "collection": {
          "label": "Collection"
        },
        "products_to_show": {
          "label": "Product count"
        },
        "columns_desktop": {
          "label": "Columns"
        },
        "show_view_all": {
          "label": "\"View all\" button",
          "info": "Visible if collection has more products than shown"
        },
        "view_all_style": {
          "label": "\"View all\" style",
          "options__1": {
            "label": "Link"
          },
          "options__2": {
            "label": "Outline button"
          },
          "options__3": {
            "label": "Solid button"
          }
        },
        "enable_desktop_slider": {
          "label": "Carousel"
        },
        "full_width": {
          "label": "Full width products"
        },
        "header": {
          "content": "Product card"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Vendor"
        },
        "show_rating": {
          "label": "Product rating",
          "info": "An app is required for ratings. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "enable_quick_buy": {
          "label": "Quick add"
        },
        "columns_mobile": {
          "label": "Columns",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "swipe_on_mobile": {
          "label": "Carousel"
        }
      },
      "presets": {
        "name": "Featured collection"
      }
    },
    "featured-product": {
      "name": "Featured product",
      "blocks": {
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "default": "Text block",
              "label": "Text"
            },
            "text_style": {
              "label": "Style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "title": {
          "name": "Title"
        },
        "price": {
          "name": "Price"
        },
        "quantity_selector": {
          "name": "Quantity selector"
        },
        "variant_picker": {
          "name": "Variant picker",
          "settings": {
            "picker_type": {
              "label": "Style",
              "options__1": {
                "label": "Dropdown"
              },
              "options__2": {
                "label": "Pills"
              }
            },
            "swatch_shape": {
              "label": "Swatch",
              "info": "Learn more about [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options",
              "options__1": {
                "label": "Circle"
              },
              "options__2": {
                "label": "Square"
              },
              "options__3": {
                "label": "None"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Buy buttons",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynamic checkout buttons",
              "info": "Customers will see their preferred payment option. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "description": {
          "name": "Description"
        },
        "share": {
          "name": "Share",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Share"
            },
            "featured_image_info": {
              "content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            }
          }
        },
        "rating": {
          "name": "Product rating",
          "settings": {
            "paragraph": {
              "content": "An app is required for product ratings. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Product"
        },
        "secondary_background": {
          "label": "Secondary background"
        },
        "header": {
          "content": "Media"
        },
        "media_position": {
          "label": "Position",
          "info": "Position is automatically optimized for mobile.",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Right"
          }
        },
        "hide_variants": {
          "label": "Hide unselected variants’ media on desktop"
        },
        "enable_video_looping": {
          "label": "Loop video"
        }
      },
      "presets": {
        "name": "Featured product"
      }
    },
    "footer": {
      "name": "Footer",
      "blocks": {
        "link_list": {
          "name": "Menu",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Quick links"
            },
            "menu": {
              "label": "Menu"
            }
          }
        },
        "brand_information": {
          "name": "Brand information",
          "settings": {
            "paragraph": {
              "content": "Manage brand info in [theme settings](/editor?context=theme&category=brand%20information)"
            },
            "show_social": {
              "label": "Social media icons",
              "info": "[Manage social accounts](/editor?context=theme&category=social%20media)"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Heading"
            },
            "subtext": {
              "label": "Subtext",
              "default": "<p>Share contact information, store details, and brand content with your customers.</p>"
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Email signup"
        },
        "newsletter_heading": {
          "label": "Heading",
          "default": "Subscribe to our emails"
        },
        "header__1": {
          "content": "Email signup",
          "info": "Signups add [customer profiles](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "show_social": {
          "label": "Social media icons",
          "info": "[Manage social accounts](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Country/region selector",
          "info": "[Manage countries/regions](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Language selector",
          "info": "[Manage languages](/admin/settings/languages)"
        },
        "payment_enable": {
          "label": "Payment method icons"
        },
        "show_policy": {
          "label": "Policy links",
          "info": "[Manage policies](/admin/settings/legal)"
        },
        "margin_top": {
          "label": "Top margin"
        },
        "header__9": {
          "content": "Utilities"
        },
        "enable_follow_on_shop": {
          "label": "Follow on Shop",
          "info": "Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        }
      }
    },
    "header": {
      "name": "Header",
      "settings": {
        "logo_help": {
          "content": "Edit your logo in [theme settings](/editor?context=theme&category=logo)"
        },
        "logo_position": {
          "label": "Logo position",
          "options__1": {
            "label": "Middle left"
          },
          "options__2": {
            "label": "Top left"
          },
          "options__3": {
            "label": "Top center"
          },
          "options__4": {
            "label": "Middle center"
          }
        },
        "menu": {
          "label": "Menu"
        },
        "menu_type_desktop": {
          "label": "Menu type",
          "options__1": {
            "label": "Dropdown"
          },
          "options__2": {
            "label": "Mega menu"
          },
          "options__3": {
            "label": "Drawer"
          }
        },
        "show_line_separator": {
          "label": "Separator line"
        },
        "header__1": {
          "content": "Color"
        },
        "menu_color_scheme": {
          "label": "Menu color scheme"
        },
        "sticky_header_type": {
          "label": "Sticky header",
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "On scroll up"
          },
          "options__3": {
            "label": "Always"
          },
          "options__4": {
            "label": "Always, reduce logo size"
          }
        },
        "header__utilities": {
          "content": "Utilities"
        },
        "enable_country_selector": {
          "label": "Country/region selector",
          "info": "[Manage countries/regions](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Language selector",
          "info": "[Manage languages](/admin/settings/languages)"
        },
        "margin_bottom": {
          "label": "Bottom margin"
        },
        "mobile_logo_position": {
          "label": "Mobile logo position",
          "options__1": {
            "label": "Center"
          },
          "options__2": {
            "label": "Left"
          }
        },
        "enable_customer_avatar": {
          "label": "Customer account avatar",
          "info": "Only visible when customers are signed in with Shop. [Manage customer accounts](/admin/settings/customer_accounts)"
        }
      }
    },
    "image-banner": {
      "name": "Image banner",
      "settings": {
        "image": {
          "label": "Image 1"
        },
        "image_2": {
          "label": "Image 2"
        },
        "image_overlay_opacity": {
          "label": "Overlay opacity"
        },
        "image_height": {
          "label": "Height",
          "options__1": {
            "label": "Adapt to first image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
          },
        "content": {
          "content": "Content"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top Left"
          },
          "options__2": {
            "label": "Top Center"
          },
          "options__3": {
            "label": "Top Right"
          },
          "options__4": {
            "label": "Middle Left"
          },
          "options__5": {
            "label": "Middle Center"
          },
          "options__6": {
            "label": "Middle Right"
          },
          "options__7": {
            "label": "Bottom Left"
          },
          "options__8": {
            "label": "Bottom Center"
          },
          "options__9": {
            "label": "Bottom Right"
          },
          "label": "Position"
        },
        "show_text_box": {
          "label": "Container"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Alignment"
        },
        "mobile": {
          "content": "Mobile layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Alignment"
        },
        "stack_images_on_mobile": {
          "label": "Stack images"
        },
        "show_text_below": {
          "label": "Container"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Image banner"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Give customers details about the banner image(s) or content on the template."
            },
            "text_style": {
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              },
              "label": "Style"
            }
          }
        },
        "buttons": {
          "name": "Buttons",
          "settings": {
          "header_1":{
          "content": "Button 1"
          },
            "button_label_1": {
              "label": "Label",
              "info": "Leave blank to hide",
              "default": "Button label"
            },
            "button_link_1": {
              "label": "Link"
            },
            "button_style_secondary_1": {
              "label": "Outline style"
            },
            "header_2":{
              "content": "Button 2"
            },
            "button_label_2": {
              "label": "Label",
              "info": "Leave blank to hide",
              "default": "Button label"
            },
            "button_link_2": {
              "label": "Link"
            },
            "button_style_secondary_2": {
              "label": "Outline style"
            }
          }
        }
      },
      "presets": {
        "name": "Image banner"
      }
    },
    "image-with-text": {
      "name": "Image with text",
      "settings": {
      "header": {
      "content": "Content"
      },
      "header_colors": {
      "content": "Colors" 
      },
        "image": {
          "label": "Image"
        },
        "height": {
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          },
          "label": "Height"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "label": "Width"
        },
        "layout": {
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Image second"
          },
          "label": "Placement"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Alignment"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Middle"
          },
          "options__3": {
            "label": "Bottom"
          },
          "label": "Position"
        },
        "content_layout": {
          "options__1": {
            "label": "No overlap"
          },
          "options__2": {
            "label": "Overlap"
          },
          "label": "Layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile alignment"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Image with text"
            }
          }
        },
        "caption": {
          "name": "Caption",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Add a tagline"
            },
            "text_style": {
              "label": "Style",
              "options__1": {
                "label": "Subtitle"
              },
              "options__2": {
                "label": "Uppercase"
              }
            },
            "caption_size": {
              "label": "Size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              }
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>"
            },
            "text_style": {
              "label": "Style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              }
            }
          }
        },
        "button": {
          "name": "Button",
          "settings": {
            "button_label": {
              "label": "Label",
              "info": "Leave blank to hide",
              "default": "Button label"
            },
            "button_link": {
              "label": "Link"
            },
            "outline_button": {
              "label": "Outline style"
            }
          }
        }
      },
      "presets": {
        "name": "Image with text"
      }
    },
    "multirow": {
      "name": "Multirow",
      "settings": {
        "image": {
          "label": "Image"
        },
        "header":{
        "content": "Image"
        },
        "header_2":{
        "content": "Content"
        },
        "header_3":{
        "content": "Colors"
        },        
        "image_height": {
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          },
          "label": "Height"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "label": "Width"
        },
        "text_style": {
          "options__1": {
            "label": "Body"
          },
          "options__2": {
            "label": "Subtitle"
          },
          "label": "Text style"
        },
        "button_style": {
          "options__1": {
            "label": "Solid button"
          },
          "options__2": {
            "label": "Outline button"
          },
          "label": "Button style"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Alignment"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Middle"
          },
          "options__3": {
            "label": "Bottom"
          },
          "label": "Position"
        },
        "image_layout": {
          "options__1": {
            "label": "Alternate from left"
          },
          "options__2": {
            "label": "Alternate from right"
          },
          "options__3": {
            "label": "Aligned left"
          },
          "options__4": {
            "label": "Aligned right"
          },
          "label": "Placement"
        },
        "container_color_scheme": {
          "label": "Container color scheme"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Mobile alignment"
        }
      },
      "blocks": {
        "row": {
          "name": "Row",
          "settings": {
            "image": {
              "label": "Image"
            },
            "caption": {
              "label": "Caption",
              "default": "Caption"
            },
            "heading": {
              "label": "Heading",
              "default": "Row"
            },
            "text": {
              "label": "Text",
              "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>"
            },
            "button_label": {
              "label": "Button label",
              "info": "Leave blank to hide",
              "default": "Button label"
            },
            "button_link": {
              "label": "Button link"
            }
          }
        }
      },
      "presets": {
        "name": "Multirow"
      }
    },
    "main-account": {
      "name": "Account"
    },
    "main-activate-account": {
      "name": "Account activation"
    },
    "main-addresses": {
      "name": "Addresses"
    },
    "main-article": {
      "name": "Blog post",
      "blocks": {
        "featured_image": {
          "name": "Featured image",
          "settings": {
            "image_height": {
              "label": "Image height",
              "options__1": {
                "label": "Adapt to image"
              },
              "options__2": {
                "label": "Small"
              },
              "options__3": {
                "label": "Medium"
              },
              "options__4": {
                "label": "Large"
              }
            }
          }
        },
        "title": {
          "name": "Title",
          "settings": {
            "blog_show_date": {
              "label": "Date"
            },
            "blog_show_author": {
              "label": "Author"
            }
          }
        },
        "content": {
          "name": "Content"
        },
        "share": {
          "name": "Share",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Share"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blog posts",
      "settings": {
        "show_image": {
          "label": "Featured image"
        },
        "show_date": {
          "label": "Date"
        },
        "show_author": {
          "label": "Author"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Grid"
          },
          "options__2": {
            "label": "Collage"
          }
        },
        "image_height": {
          "label": "Image height",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        }
      }
    },
    "main-cart-footer": {
      "name": "Subtotal",
      "blocks": {
        "subtotal": {
          "name": "Subtotal price"
        },
        "buttons": {
          "name": "Checkout button"
        }
      }
    },
    "main-cart-items": {
      "name": "Items"
    },
    "main-collection-banner": {
      "name": "Collection banner",
      "settings": {
        "paragraph": {
          "content": "Collection details are [managed in your admin](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Description"
        },
        "show_collection_image": {
          "label": "Image"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Product grid",
      "settings": {
        "products_per_page": {
          "label": "Products per page"
        },
        "columns_desktop": {
          "label": "Columns"
        },
        "enable_filtering": {
          "label": "Filters",
          "info": "Customize filters with the [Search & Discovery app](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "filter_type": {
          "label": "Filter layout",
          "options__1": {
            "label": "Horizontal"
          },
          "options__2": {
            "label": "Vertical"
          },
          "options__3": {
            "label": "Drawer"
          }
        },
        "enable_sorting": {
          "label": "Sorting"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Vendor"
        },
        "show_rating": {
          "label": "Product rating",
          "info": "An app is required for product ratings. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"
        },
        "header__1": {
          "content": "Filtering and sorting"
        },
        "header__3": {
          "content": "Product card"
        },
        "enable_tags": {
          "label": "Filters",
          "info": "Customize filters with the [Search & Discovery app](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "quick_add": {
          "label": "Quick add",
          "options": {
            "option_1": "None",
            "option_2": "Standard",
            "option_3": "Bulk"
          }
        },
        "columns_mobile": {
          "label": "Mobile columns",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "main-list-collections": {
      "name": "Collections list page",
      "settings": {
        "title": {
          "label": "Heading",
          "default": "Collections"
        },
        "sort": {
          "label": "Sort collections",
          "options__1": {
            "label": "Alphabetically, A-Z"
          },
          "options__2": {
            "label": "Alphabetically, Z-A"
          },
          "options__3": {
            "label": "Date, new to old"
          },
          "options__4": {
            "label": "Date, old to new"
          },
          "options__5": {
            "label": "Product count, high to low"
          },
          "options__6": {
            "label": "Product count, low to high"
          }
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "columns_desktop": {
          "label": "Columns"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Mobile columns",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "main-login": {
      "name": "Login",
      "shop_login_button": {
        "enable": "Enable Sign in with Shop"
      }
    },
    "main-order": {
      "name": "Order"
    },
    "main-page": {
      "name": "Page"
    },
    "main-password-footer": {
      "name": "Password footer"
    },
    "main-password-header": {
      "name": "Password header",
      "settings": {
        "logo_help": {
          "content": "Edit your logo in [theme settings](/editor?context=theme&category=logo)"
        }
      }
    },
    "main-product": {
      "name": "Product information",
      "blocks": {
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Text block"
            },
            "text_style": {
              "label": "Style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "title": {
          "name": "Title"
        },
        "price": {
          "name": "Price"
        },
        "inventory": {
          "name": "Inventory status",
          "settings": {
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            },
            "inventory_threshold": {
              "label": "Low inventory threshold"
            },
            "show_inventory_quantity": {
              "label": "Inventory count"
            }
          }
        },
        "quantity_selector": {
          "name": "Quantity selector"
        },
        "variant_picker": {
          "name": "Variant picker",
          "settings": {
            "picker_type": {
              "label": "Style",
              "options__1": {
                "label": "Dropdown"
              },
              "options__2": {
                "label": "Pills"
              }
            },
            "swatch_shape": {
              "label": "Swatch",
              "info": "Learn more about [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options",
              "options__1": {
                "label": "Circle"
              },
              "options__2": {
                "label": "Square"
              },
              "options__3": {
                "label": "None"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Buy buttons",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynamic checkout buttons",
              "info": "Customers will see their preferred payment option. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            },
            "show_gift_card_recipient": {
              "label": " Gift card sending options",
              "info": "Customers can add a personal message and schedule the send date. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"
            }
          }
        },
        "pickup_availability": {
          "name": "Pickup availability"
        },
        "description": {
          "name": "Description"
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Text style",
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "options__3": {
                "label": "Uppercase"
              }
            }
          }
        },
        "share": {
          "name": "Share",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Share"
            }
          }
        },
        "collapsible_tab": {
          "name": "Collapsible row",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Collapsible row"
            },
            "content": {
              "label": "Row content"
            },
            "page": {
              "label": "Row content from page"
            },
            "icon": {
              "label": "Icon",
              "options__1": {
                "label": "None"
              },
              "options__2": {
                "label": "Apple"
              },
              "options__3": {
                "label": "Banana"
              },
              "options__4": {
                "label": "Bottle"
              },
              "options__5": {
                "label": "Box"
              },
              "options__6": {
                "label": "Carrot"
              },
              "options__7": {
                "label": "Chat bubble"
              },
              "options__8": {
                "label": "Check mark"
              },
              "options__9": {
                "label": "Clipboard"
              },
              "options__10": {
                "label": "Dairy"
              },
              "options__11": {
                "label": "Dairy free"
              },
              "options__12": {
                "label": "Dryer"
              },
              "options__13": {
                "label": "Eye"
              },
              "options__14": {
                "label": "Fire"
              },
              "options__15": {
                "label": "Gluten free"
              },
              "options__16": {
                "label": "Heart"
              },
              "options__17": {
                "label": "Iron"
              },
              "options__18": {
                "label": "Leaf"
              },
              "options__19": {
                "label": "Leather"
              },
              "options__20": {
                "label": "Lightning bolt"
              },
              "options__21": {
                "label": "Lipstick"
              },
              "options__22": {
                "label": "Lock"
              },
              "options__23": {
                "label": "Map pin"
              },
              "options__24": {
                "label": "Nut free"
              },
              "options__25": {
                "label": "Pants"
              },
              "options__26": {
                "label": "Paw print"
              },
              "options__27": {
                "label": "Pepper"
              },
              "options__28": {
                "label": "Perfume"
              },
              "options__29": {
                "label": "Plane"
              },
              "options__30": {
                "label": "Plant"
              },
              "options__31": {
                "label": "Price tag"
              },
              "options__32": {
                "label": "Question mark"
              },
              "options__33": {
                "label": "Recycle"
              },
              "options__34": {
                "label": "Return"
              },
              "options__35": {
                "label": "Ruler"
              },
              "options__36": {
                "label": "Serving dish"
              },
              "options__37": {
                "label": "Shirt"
              },
              "options__38": {
                "label": "Shoe"
              },
              "options__39": {
                "label": "Silhouette"
              },
              "options__40": {
                "label": "Snowflake"
              },
              "options__41": {
                "label": "Star"
              },
              "options__42": {
                "label": "Stopwatch"
              },
              "options__43": {
                "label": "Truck"
              },
              "options__44": {
                "label": "Washing"
              }
            }
          }
        },
        "popup": {
          "name": "Pop-up",
          "settings": {
            "link_label": {
              "label": "Link label",
              "default": "Pop-up link text"
            },
            "page": {
              "label": "Page"
            }
          }
        },
        "rating": {
          "name": "Product rating",
          "settings": {
            "paragraph": {
              "content": "An app is required for product ratings. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Complementary products",
          "settings": {
            "paragraph": {
              "content": "Manage complementary products in the [Search & Discovery app](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Heading",
              "default": "Pairs well with"
            },
            "make_collapsible_row": {
              "label": "Collapsible row"
            },
            "icon": {
              "info": "Shown when collapsible row is selected"
            },
            "product_list_limit": {
              "label": "Product count"
            },
            "products_per_page": {
              "label": "Products per page"
            },
            "pagination_style": {
              "label": "Pagination",
              "options": {
                "option_1": "Dots",
                "option_2": "Counter",
                "option_3": "Numbers"
              }
            },
            "product_card": {
              "heading": "Product card"
            },
            "image_ratio": {
              "label": "Image ratio",
              "options": {
                "option_1": "Portrait",
                "option_2": "Square"
              }
            },
            "enable_quick_add": {
              "label": "Quick add"
            }
          }
        },
        "icon_with_text": {
          "name": "Icon with text",
          "settings": {
            "layout": {
              "label": "Layout",
              "options__1": {
                "label": "Horizontal"
              },
              "options__2": {
                "label": "Vertical"
              }
            },
            "pairing_1": {
              "label": "Pairing 1",
              "info": "Choose an icon or add an image for each pairing"
            },
            "heading": {
              "info": "Leave blank to hide this pairing"
            },
            "icon_1": {
              "label": "Icon"
            },
            "image_1": {
              "label": "Image"
            },
            "heading_1": {
              "label": "Heading",
              "default": "Heading"
            },
            "pairing_2": {
              "label": "Pairing 2"
            },            
            "icon_2": {
              "label": "Icon"
            },
            "image_2": {
              "label": "Image"
            },
            "heading_2": {
              "label": "Heading",
              "default": "Heading"
            },
            "pairing_3": {
            "label": "Pairing 3"
            },             
            "icon_3": {
              "label": "Icon"
            },
            "image_3": {
              "label": "Image"
            },
            "heading_3": {
              "label": "Heading",
              "default": "Heading"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Media"
        },
        "enable_sticky_info": {
          "label": "Sticky content"
        },
        "gallery_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Stacked"
          },
          "options__2": {
            "label": "2 columns"
          },
          "options__3": {
            "label": "Thumbnails"
          },
          "options__4": {
            "label": "Thumbnail carousel"
          }
        },
        "constrain_to_viewport": {
          "label": "Constrain to screen height"
        },
        "media_size": {
          "label": "Width",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "image_zoom": {
          "label": "Zoom",
          "options__1": {
            "label": "Open lightbox"
          },
          "options__2": {
            "label": "Click and hover"
          },
          "options__3": {
            "label": "No zoom"
          }
        },
        "media_position": {
          "label": "Position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Right"
          }
        },
        "media_fit": {
          "label": "Fit",
          "options__1": {
            "label": "Original"
          },
          "options__2": {
            "label": "Fill"
          }
        },
        "mobile_thumbnails": {
          "label": "Mobile layout",
          "options__1": {
            "label": "2 columns"
          },
          "options__2": {
            "label": "Show thumbnails"
          },
          "options__3": {
            "label": "Hide thumbnails"
          }
        },
        "hide_variants": {
          "label": "Hide other variant media after one is selected"
        },
        "enable_video_looping": {
          "label": "Loop video"
        }
      }
    },
    "main-register": {
      "name": "Registration"
    },
    "main-reset-password": {
      "name": "Password reset"
    },
    "main-search": {
      "name": "Search results",
      "settings": {
        "columns_desktop": {
          "label": "Columns"
        },
        "columns_mobile": {
          "label": "Mobile columns",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },        
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Vendor"
        },
        "show_rating": {
          "label": "Product rating",
          "info": "An app is required for product ratings. [Learn more](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"
        },
        "header__1": {
          "content": "Product card"
        },
        "header__2": {
          "content": "Blog card"
        },
        "article_show_date": {
          "label": "Date"
        },
        "article_show_author": {
          "label": "Author"
        }
      }
    },
    "multicolumn": {
      "name": "Multicolumn",
      "settings": {
        "title": {
          "label": "Heading",
          "default": "Multicolumn"
        },
        "header_text": {
          "content": "Heading"
        },
        "header_image": {
          "content": "Image"
        },
        "header_layout": {
          "content": "Layout"
        },                            
        "header_button": {
          "content": "Button"
        }, 
        "image_width": {
          "label": "Width",
          "options__1": {
            "label": "One-third width of column"
          },
          "options__2": {
            "label": "Half width of column"
          },
          "options__3": {
            "label": "Full width of column"
          }
        },
        "image_ratio": {
          "label": "Ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          },
          "options__4": {
            "label": "Circle"
          }
        },
        "columns_desktop": {
          "label": "Columns"
        },
        "column_alignment": {
          "label": "Column alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "background_style": {
          "label": "Secondary background",
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "Show as column background"
          }
        },
        "button_label": {
          "label": "Label",
          "default": "Button label",
          "info": "Leave blank to hide"
        },
        "button_link": {
          "label": "Link"
        },
        "header_mobile": {
          "content": "Mobile layout"
        },
        "columns_mobile": {
          "label": "Columns",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "swipe_on_mobile": {
          "label": "Carousel"
        }
      },
      "blocks": {
        "column": {
          "name": "Column",
          "settings": {
            "image": {
              "label": "Image"
            },
            "title": {
              "label": "Heading",
              "default": "Column"
            },
            "text": {
              "label": "Description",
              "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>"
            },
            "link_label": {
              "label": "Link label",
              "info": "Leave blank to hide"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Multicolumn"
      }
    },
    "newsletter": {
      "name": "Email signup",
      "settings": {
        "full_width": {
          "label": "Full width"
        },
        "paragraph": {
          "content": "Signups add [customer profiles](https://help.shopify.com/manual/customers/manage-customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Subscribe to our emails"
            }
          }
        },
        "paragraph": {
          "name": "Text",
          "settings": {
            "paragraph": {
              "label": "Text",
              "default": "<p>Be the first to know about new collections and exclusive offers.</p>"
            }
          }
        },
        "email_form": {
          "name": "Email form"
        }
      },
      "presets": {
        "name": "Email signup"
      }
    },
    "email-signup-banner": {
      "name": "Email signup banner",
      "settings": {
        "paragraph": {
          "content": "Signups add [customer profiles](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "image": {
          "label": "Background image"
        },
        "image_overlay_opacity": {
          "label": "Overlay opacity"
        },
        "show_background_image": {
          "label": "Show background image"
        },
        "show_text_box": {
          "label": "Container"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Top Left"
          },
          "options__2": {
            "label": "Top Center"
          },
          "options__3": {
            "label": "Top Right"
          },
          "options__4": {
            "label": "Middle Left"
          },
          "options__5": {
            "label": "Middle Center"
          },
          "options__6": {
            "label": "Middle Right"
          },
          "options__7": {
            "label": "Bottom Left"
          },
          "options__8": {
            "label": "Bottom Center"
          },
          "options__9": {
            "label": "Bottom Right"
          },
          "label": "Position"
        },
        "color_scheme": {
          "info": "Visible when container displayed."
        },
        "image_height": {
          "label": "Height",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "content_header": {
          "content": "Content"
        },        
        "desktop_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Alignment"
        },
        "header": {
          "content": "Mobile layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Alignment"
        },
        "show_text_below": {
          "label": "Stack text below image"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Opening soon"
            }
          }
        },
        "paragraph": {
          "name": "Text",
          "settings": {
            "paragraph": {
              "label": "Text",
              "default": "<p>Be the first to know when we launch.</p>"
            },
            "text_style": {
              "options__1": {
                "label": "Body"
              },
              "options__2": {
                "label": "Subtitle"
              },
              "label": "Style"
            }
          }
        },
        "email_form": {
          "name": "Email form"
        }
      },
      "presets": {
        "name": "Email signup banner"
      }
    },
    "page": {
      "name": "Page",
      "settings": {
        "page": {
          "label": "Page"
        }
      },
      "presets": {
        "name": "Page"
      }
    },
    "quick-order-list": {
      "name": "Quick order list",
      "settings": {
        "variants_per_page": {
          "label": "Variants per page"
        },
        "show_image": {
          "label": "Images"
        },
        "show_sku": {
          "label": "SKUs"
        }
      },
      "presets": {
        "name": "Quick order list"
      }
    },
    "related-products": {
      "name": "Related products",
      "settings": {
        "heading": {
          "label": "Heading"
        },
        "products_to_show": {
          "label": "Product count"
        },
        "columns_desktop": {
          "label": "Columns"
        },
        "paragraph__1": {
          "content": "Related products can be managed in the [Search & Discovery app](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)",
          "default": "You may also like"
        },
        "header__2": {
          "content": "Product card"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Portrait"
          },
          "options__3": {
            "label": "Square"
          }
        },
        "show_secondary_image": {
          "label": "Show second image on hover"
        },
        "show_vendor": {
          "label": "Vendor"
        },
        "show_rating": {
          "label": "Product rating",
          "info": "An app is required for product ratings. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"
        },
        "columns_mobile": {
          "label": "Mobile columns",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "rich-text": {
      "name": "Rich text",
      "settings": {
        "desktop_content_position": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Content position"
        },
        "content_alignment": {
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          },
          "label": "Content alignment"
        },
        "full_width": {
          "label": "Full width"
        }
      },
      "blocks": {
        "heading": {
          "name": "Heading",
          "settings": {
            "heading": {
              "label": "Heading",
              "default": "Talk about your brand"
            }
          }
        },
        "caption": {
          "name": "Caption",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Add a tagline"
            },
            "text_style": {
              "label": "Style",
              "options__1": {
                "label": "Subtitle"
              },
              "options__2": {
                "label": "Uppercase"
              }
            },
            "caption_size": {
              "label": "Size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              }
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>"
            }
          }
        },
        "buttons": {
          "name": "Buttons",
          "settings": {
            "header_button1": {
            "content": "Button 1"
            }, 
            "button_label_1": {
              "label": "Label",
              "info": "Leave blank to hide",
              "default": "Button label"
            },
            "button_link_1": {
              "label": "Link"
            },
            "button_style_secondary_1": {
              "label": "Outline style"
            },
            "header_button2": {
              "content": "Button 2"
            },             
            "button_label_2": {
              "label": "Label",
              "info": "Leave label blank to hide"
            },
            "button_link_2": {
              "label": "Link"
            },
            "button_style_secondary_2": {
              "label": "Outline style"
            }
          }
        }
      },
      "presets": {
        "name": "Rich text"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Heading",
          "default": "Video"
        },
        "cover_image": {
          "label": "Cover image"
        },
        "video": {
          "label": "Video"
        },
        "enable_video_looping": {
          "label": "Loop video"
        },
        "header__1": {
          "content": "Shopify-hosted video"
        },
        "header__2": {
          "content": "Or embed video from URL"
        },
        "header__3": {
          "content": "Layout"
        },
        "paragraph": {
          "content": "Shows when no Shopify-hosted video is selected"
        },
        "video_url": {
          "label": "URL",
          "info": "Use a YouTube or Vimeo URL"
        },
        "description": {
          "label": "Video alt text",
          "info": "Describe the video for those using screen readers"
        },
        "image_padding": {
          "label": "Add image padding",
          "info": "Select image padding if you don't want your cover image to be cropped."
        },
        "full_width": {
          "label": "Full width"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "slideshow": {
      "name": "Slideshow",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Full width"
          },
          "options__2": {
            "label": "Page"
          }
        },
        "slide_height": {
          "label": "Height",
          "options__1": {
            "label": "Adapt to first image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "slider_visual": {
          "label": "Pagination",
          "options__1": {
            "label": "Counter"
          },
          "options__2": {
            "label": "Dots"
          },
          "options__3": {
            "label": "Numbers"
          }
        },
        "auto_rotate": {
          "label": "Auto rotate slides"
        },
        "change_slides_speed": {
          "label": "Change slides every"
        },
        "mobile": {
          "content": "Mobile layout"
        },
        "show_text_below": {
          "label": "Stack text below image"
        },
        "accessibility": {
          "content": "Accessibility",
          "label": "Slideshow description",
          "info": "Describe the slideshow for those using screen readers",
          "default": "Slideshow about our brand"
        }
      },
      "blocks": {
        "slide": {
          "name": "Slide",
          "settings": {
            "image": {
              "label": "Image"
            },
            "heading": {
              "label": "Heading",
              "default": "Image slide"
            },
            "subheading": {
              "label": "Subheading",
              "default": "Tell your brand's story through images"
            },
            "header_button": {
            "content": "Button"      
            },
            "button_label": {
              "label": "Label",
              "default": "Button label",
              "info": "Leave blank to hide"
            },
            "link": {
              "label": "Link"
            },
            "secondary_style": {
              "label": "Outline style"
            },
            "box_align": {
              "label": "Content position",              
              "options__1": {
                "label": "Top left"
              },
              "options__2": {
                "label": "Top center"
              },
              "options__3": {
                "label": "Top right"
              },
              "options__4": {
                "label": "Middle left"
              },
              "options__5": {
                "label": "Middle center"
              },
              "options__6": {
                "label": "Middle right"
              },
              "options__7": {
                "label": "Bottom left"
              },
              "options__8": {
                "label": "Bottom center"
              },
              "options__9": {
                "label": "Bottom right"
              }
            },
            "header_layout": {
            "content": "Layout"                 
            },
            "show_text_box": {
              "label": "Container"
            },
            "text_alignment": {
              "label": "Content alignment",
              "option_1": {
                "label": "Left"
              },
              "option_2": {
                "label": "Center"
              },
              "option_3": {
                "label": "Right"
              }
            },
            "image_overlay_opacity": {
              "label": "Overlay opacity"
            },
            "header_text": {
            "content": "Text"      
            },
            "header_colors": {
            "content": "Colors"                 
            },            
            "text_alignment_mobile": {
              "label": "Mobile content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              },
              "options__3": {
                "label": "Right"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Slideshow"
      }
    },
    "collapsible_content": {
      "name": "Collapsible content",
      "settings": {
        "caption": {
          "label": "Caption"
        },
        "heading": {
          "label": "Heading",
          "default": "Collapsible content"
        },
        "heading_alignment": {
          "label": "Heading alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "layout_header": {
          "content": "Layout"
        },
        "layout": {
          "label": "Container",
          "options__1": {
            "label": "No container"
          },
          "options__2": {
            "label": "Row container"
          },
          "options__3": {
            "label": "Section container"
          }
        },
        "container_color_scheme": {
          "label": "Container color scheme"
        },
        "section_color_scheme": {
        "label": "Section color scheme"
        },        
        "open_first_collapsible_row": {
          "label": "Open first row"
        },
        "header": {
          "content": "Image"
        },
        "image": {
          "label": "Image"
        },
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Adapt to image"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "desktop_layout": {
          "label": "Placement",
          "options__1": {
            "label": "Image first"
          },
          "options__2": {
            "label": "Image second"
          }
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Collapsible row",
          "settings": {
            "heading": {
              "default": "Collapsible row",
              "label": "Heading"
            },
            "row_content": {
              "label": "Row content"
            },
            "page": {
              "label": "Row content from page"
            },
            "icon": {
              "label": "Icon",
              "options__1": {
                "label": "None"
              },
              "options__2": {
                "label": "Apple"
              },
              "options__3": {
                "label": "Banana"
              },
              "options__4": {
                "label": "Bottle"
              },
              "options__5": {
                "label": "Box"
              },
              "options__6": {
                "label": "Carrot"
              },
              "options__7": {
                "label": "Chat bubble"
              },
              "options__8": {
                "label": "Check mark"
              },
              "options__9": {
                "label": "Clipboard"
              },
              "options__10": {
                "label": "Dairy"
              },
              "options__11": {
                "label": "Dairy free"
              },
              "options__12": {
                "label": "Dryer"
              },
              "options__13": {
                "label": "Eye"
              },
              "options__14": {
                "label": "Fire"
              },
              "options__15": {
                "label": "Gluten free"
              },
              "options__16": {
                "label": "Heart"
              },
              "options__17": {
                "label": "Iron"
              },
              "options__18": {
                "label": "Leaf"
              },
              "options__19": {
                "label": "Leather"
              },
              "options__20": {
                "label": "Lightning bolt"
              },
              "options__21": {
                "label": "Lipstick"
              },
              "options__22": {
                "label": "Lock"
              },
              "options__23": {
                "label": "Map pin"
              },
              "options__24": {
                "label": "Nut free"
              },
              "options__25": {
                "label": "Pants"
              },
              "options__26": {
                "label": "Paw print"
              },
              "options__27": {
                "label": "Pepper"
              },
              "options__28": {
                "label": "Perfume"
              },
              "options__29": {
                "label": "Plane"
              },
              "options__30": {
                "label": "Plant"
              },
              "options__31": {
                "label": "Price tag"
              },
              "options__32": {
                "label": "Question mark"
              },
              "options__33": {
                "label": "Recycle"
              },
              "options__34": {
                "label": "Return"
              },
              "options__35": {
                "label": "Ruler"
              },
              "options__36": {
                "label": "Serving dish"
              },
              "options__37": {
                "label": "Shirt"
              },
              "options__38": {
                "label": "Shoe"
              },
              "options__39": {
                "label": "Silhouette"
              },
              "options__40": {
                "label": "Snowflake"
              },
              "options__41": {
                "label": "Star"
              },
              "options__42": {
                "label": "Stopwatch"
              },
              "options__43": {
                "label": "Truck"
              },
              "options__44": {
                "label": "Washing"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Collapsible content"
      }
    }
  }
}
