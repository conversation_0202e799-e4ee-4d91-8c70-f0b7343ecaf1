/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Farben",
      "settings": {
        "background": {
          "label": "Hintergrund"
        },
        "background_gradient": {
          "label": "Hintergrundfarbverlauf",
          "info": "Wo möglich, ersetzt der Hintergrundfarbverlauf den Hintergrund."
        },
        "text": {
          "label": "Text"
        },
        "button_background": {
          "label": "Hintergrund für durchgehende Schaltfläche"
        },
        "button_label": {
          "label": "Beschriftung für durchgehende Schaltfläche"
        },
        "secondary_button_label": {
          "label": "Umriss-Schaltfläche"
        },
        "shadow": {
          "label": "Schatten"
        }
      }
    },
    "typography": {
      "name": "Typografie",
      "settings": {
        "type_header_font": {
          "label": "Schriftart"
        },
        "header__1": {
          "content": "Überschriften"
        },
        "header__2": {
          "content": "Nachricht"
        },
        "type_body_font": {
          "label": "Schriftart"
        },
        "heading_scale": {
          "label": "Maßstab"
        },
        "body_scale": {
          "label": "Maßstab"
        }
      }
    },
    "social-media": {
      "name": "Social Media",
      "settings": {
        "social_twitter_link": {
          "label": "X / Twitter",
          "info": "https://x.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://vimeo.com/shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "http://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Social-Media-Konten"
        }
      }
    },
    "currency_format": {
      "name": "Währungsformat",
      "settings": {
        "currency_code_enabled": {
          "label": "Währungscodes"
        },
        "paragraph": "Warenkorb- und Checkout-Preise zeigen immer Währungscodes an"
      }
    },
    "layout": {
      "name": "Layout",
      "settings": {
        "page_width": {
          "label": "Seitenbreite"
        },
        "spacing_sections": {
          "label": "Platz zwischen Vorlagenabschnitten"
        },
        "header__grid": {
          "content": "Raster"
        },
        "paragraph__grid": {
          "content": "Wirkt sich auf Bereiche mit mehreren Spalten oder Reihen aus"
        },
        "spacing_grid_horizontal": {
          "label": "Horizontaler Abstand"
        },
        "spacing_grid_vertical": {
          "label": "Vertikaler Abstand"
        }
      }
    },
    "search_input": {
      "name": "Suchverhalten",
      "settings": {
        "predictive_search_enabled": {
          "label": "Suchvorschläge"
        },
        "predictive_search_show_vendor": {
          "label": "Produktanbieter",
          "info": "Wird angezeigt, wenn Suchvorschläge aktiviert sind"
        },
        "predictive_search_show_price": {
          "label": "Produktpreis",
          "info": "Wird angezeigt, wenn Suchvorschläge aktiviert sind"
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Rand"
        },
        "header__shadow": {
          "content": "Schatten"
        },
        "blur": {
          "label": "Weichzeichnen"
        },
        "corner_radius": {
          "label": "Eckradius"
        },
        "horizontal_offset": {
          "label": "Horizontaler Offset"
        },
        "vertical_offset": {
          "label": "Vertikaler Offset"
        },
        "thickness": {
          "label": "Dicke"
        },
        "opacity": {
          "label": "Opazität"
        },
        "image_padding": {
          "label": "Bild-Padding"
        },
        "text_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Textausrichtung"
        }
      }
    },
    "badges": {
      "name": "Badges",
      "settings": {
        "position": {
          "options__1": {
            "label": "Unten links"
          },
          "options__2": {
            "label": "Unten rechts"
          },
          "options__3": {
            "label": "Oben links"
          },
          "options__4": {
            "label": "Oben rechts"
          },
          "label": "Position auf Karten"
        },
        "sale_badge_color_scheme": {
          "label": "Farbschema für Sale-Badges"
        },
        "sold_out_badge_color_scheme": {
          "label": "Farbschema für Ausverkauft-Badges"
        }
      }
    },
    "buttons": {
      "name": "Schaltflächen"
    },
    "variant_pills": {
      "name": "Varianten-Kapseln",
      "paragraph": "Varianten-Kapseln sind eine Möglichkeit, deine [Produktvarianten](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block) zu präsentieren"
    },
    "inputs": {
      "name": "Eingaben"
    },
    "content_containers": {
      "name": "Inhalts-Container"
    },
    "popups": {
      "name": "Dropdown-Listen und Pop-ups",
      "paragraph": "Wirkt sich auf Bereiche wie das Dropdown-Menü für die Navigation, modale Pop-ups und Warenkorb-Pop-ups aus"
    },
    "media": {
      "name": "Medien"
    },
    "drawers": {
      "name": "Einschübe"
    },
    "cart": {
      "name": "Warenkorb",
      "settings": {
        "cart_type": {
          "label": "Art",
          "drawer": {
            "label": "Einschub"
          },
          "page": {
            "label": "Seite"
          },
          "notification": {
            "label": "Pop-up-Benachrichtigung"
          }
        },
        "show_vendor": {
          "label": "Anbieter"
        },
        "show_cart_note": {
          "label": "Warenkorbanmerkung"
        },
        "cart_drawer": {
          "header": "Warenkorbeinschub",
          "collection": {
            "label": "Kollektion",
            "info": "Wird angezeigt, wenn der Warenkorbeinschub leer ist"
          }
        }
      }
    },
    "cards": {
      "name": "Produktkarten",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Karte"
          },
          "label": "Optik"
        }
      }
    },
    "collection_cards": {
      "name": "Kollektionskarten",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Karte"
          },
          "label": "Optik"
        }
      }
    },
    "blog_cards": {
      "name": "Blog-Karten",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "Karte"
          },
          "label": "Optik"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Breite"
        },
        "favicon": {
          "label": "Favicon",
          "info": "Mit 32 px x 32 px angezeigt"
        }
      }
    },
    "brand_information": {
      "name": "Markeninformationen",
      "settings": {
        "brand_headline": {
          "label": "Überschrift"
        },
        "brand_description": {
          "label": "Beschreibung"
        },
        "brand_image": {
          "label": "Bild"
        },
        "brand_image_width": {
          "label": "Bildbreite"
        },
        "paragraph": {
          "content": "Wird in der Fußzeile des Markeninfoblocks angezeigt"
        }
      }
    },
    "animations": {
      "name": "Animationen",
      "settings": {
        "animations_reveal_on_scroll": {
          "label": "Beim Scrollen Abschnitte einblenden"
        },
        "animations_hover_elements": {
          "options__1": {
            "label": "Keiner"
          },
          "options__2": {
            "label": "Vertikal-Lift"
          },
          "label": "Hover-Effekt",
          "info": "Wirkt sich auf Karten und Schaltflächen aus",
          "options__3": {
            "label": "3D-Lift"
          }
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Padding",
        "padding_top": "Oben",
        "padding_bottom": "Unten"
      },
      "spacing": "Abstand",
      "colors": {
        "label": "Farbschema",
        "has_cards_info": "Aktualisiere deine Theme-Einstellungen, um das Farbschema der Karte zu ändern."
      },
      "heading_size": {
        "label": "Größe der Überschrift",
        "options__1": {
          "label": "Klein"
        },
        "options__2": {
          "label": "Mittel"
        },
        "options__3": {
          "label": "Groß"
        },
        "options__4": {
          "label": "Extra groß"
        },
        "options__5": {
          "label": "Extra, extra groß"
        }
      },
      "image_shape": {
        "options__1": {
          "label": "Standard"
        },
        "options__2": {
          "label": "Bogen"
        },
        "options__3": {
          "label": "Klecks"
        },
        "options__4": {
          "label": "Chevron nach links"
        },
        "options__5": {
          "label": "Chevron nach rechts"
        },
        "options__6": {
          "label": "Diamant"
        },
        "options__7": {
          "label": "Parallelogramm"
        },
        "options__8": {
          "label": "Rund"
        },
        "label": "Bildform"
      },
      "animation": {
        "content": "Animationen",
        "image_behavior": {
          "options__1": {
            "label": "Keine(r)"
          },
          "options__2": {
            "label": "Atmosphärische Bewegung"
          },
          "label": "Animation",
          "options__3": {
            "label": "Feste Hintergrundposition"
          },
          "options__4": {
            "label": "Beim Scrollen heranzoomen"
          }
        }
      }
    },
    "announcement-bar": {
      "name": "Ankündigungsleiste",
      "blocks": {
        "announcement": {
          "name": "Ankündigung",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Willkommen in unserem Shop"
            },
            "link": {
              "label": "Link"
            },
            "text_alignment": {
              "label": "Textausrichtung",
              "options__1": {
                "label": "Links"
              },
              "options__2": {
                "label": "Zentriert"
              },
              "options__3": {
                "label": "Rechts"
              }
            }
          }
        }
      },
      "settings": {
        "auto_rotate": {
          "label": "Automatisch rotierende Ankündigungen"
        },
        "change_slides_speed": {
          "label": "Ändern alle"
        },
        "show_social": {
          "label": "Social Media-Symbole",
          "info": "[Social-Media-Konten verwalten](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Auswahl für Land/Region",
          "info": "[Länder/Regionen verwalten](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Sprachauswahl",
          "info": "[Sprachen verwalten](/admin/settings/languages)"
        },
        "heading_utilities": {
          "content": "Versorgungsunternehmen"
        },
        "paragraph": {
          "content": "Wird nur auf großen Bildschirmen angezeigt"
        }
      },
      "presets": {
        "name": "Ankündigungsleiste"
      }
    },
    "collage": {
      "name": "Collage",
      "settings": {
        "heading": {
          "label": "Überschrift",
          "default": "Multimedia-Collage"
        },
        "desktop_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Großer Block zuerst"
          },
          "options__2": {
            "label": "Großer Block zuletzt"
          }
        },
        "mobile_layout": {
          "label": "Mobiles Layout",
          "options__1": {
            "label": "Collage"
          },
          "options__2": {
            "label": "Spalte"
          }
        },
        "card_styles": {
          "label": "Kartendesign",
          "info": "Individuelle Kartendesigns verwalten [Theme-Einstellungen](/editor?context=theme&category=product%20cards)",
          "options__1": {
            "label": "Individuelle Kartendesigns verwenden"
          },
          "options__2": {
            "label": "Alle als Produktkarten gestalten"
          }
        },
        "header_layout": {
          "content": "Layout"
        }
      },
      "blocks": {
        "image": {
          "name": "Bild",
          "settings": {
            "image": {
              "label": "Bild"
            }
          }
        },
        "product": {
          "name": "Produkt",
          "settings": {
            "product": {
              "label": "Produkt"
            },
            "secondary_background": {
              "label": "Sekundären Hintergrund anzeigen"
            },
            "second_image": {
              "label": "Hover-Effekt mit zweitem Bild"
            }
          }
        },
        "collection": {
          "name": "Kategorie",
          "settings": {
            "collection": {
              "label": "Kategorie"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Titelbild"
            },
            "video_url": {
              "label": "URL",
              "info": "Video wird in einem Pop-up abgespielt, wenn der Abschnitt andere Blöcke enthält.",
              "placeholder": "YouTube- oder Vimeo-URL verwenden"
            },
            "description": {
              "label": "Video-Alt-Text",
              "info": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
              "default": "Beschreibe das Video"
            }
          }
        }
      },
      "presets": {
        "name": "Collage"
      }
    },
    "collection-list": {
      "name": "Kollektionsliste",
      "settings": {
        "title": {
          "label": "Überschrift",
          "default": "Kollektionen"
        },
        "image_ratio": {
          "label": "Bildverhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Porträt"
          },
          "options__3": {
            "label": "Quadrat"
          }
        },
        "swipe_on_mobile": {
          "label": "Karussell"
        },
        "show_view_all": {
          "label": "Schaltfläche \"Alle anzeigen\"",
          "info": "Sichtbar, wenn die Liste mehr als die angezeigten Kollektionen enthält"
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "header_mobile": {
          "content": "Mobiles Layout"
        },
        "columns_mobile": {
          "label": "Spalten",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_layout": {
          "content": "Layout"
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Kategorie",
          "settings": {
            "collection": {
              "label": "Kategorie"
            }
          }
        }
      },
      "presets": {
        "name": "Kollektionsliste"
      }
    },
    "contact-form": {
      "name": "Kontaktformular",
      "presets": {
        "name": "Kontaktformular"
      },
      "settings": {
        "title": {
          "default": "Kontaktformular",
          "label": "Titel"
        }
      }
    },
    "custom-liquid": {
      "name": "Benutzerdefiniertes Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Liquid-Code",
          "info": "Füge App-Snippets oder anderen Code hinzu, um fortgeschrittene Anpassungen zu erstellen. [Mehr Informationen](https://shopify.dev/docs/api/liquid)"
        }
      },
      "presets": {
        "name": "Benutzerdefiniertes Liquid"
      }
    },
    "featured-blog": {
      "name": "Blog-Beiträge",
      "settings": {
        "heading": {
          "label": "Überschrift",
          "default": "Blog-Beiträge"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Beitragsanzahl"
        },
        "show_view_all": {
          "label": "Schaltfläche \"Alle anzeigen\"",
          "info": "Sichtbar, wenn der Blog mehr als die angezeigten Beiträge enthält"
        },
        "show_image": {
          "label": "Feature-Bild"
        },
        "show_date": {
          "label": "Datum"
        },
        "show_author": {
          "label": "Autor"
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "layout_header": {
          "content": "Layout"
        },
        "text_header": {
          "content": "Text"
        }
      },
      "presets": {
        "name": "Blog-Beiträge"
      }
    },
    "featured-collection": {
      "name": "Vorgestellte Kollektion",
      "settings": {
        "title": {
          "label": "Überschrift",
          "default": "Vorgestellte Kollektion"
        },
        "collection": {
          "label": "Kategorie"
        },
        "products_to_show": {
          "label": "Produktanzahl"
        },
        "show_view_all": {
          "label": "Schaltfläche \"Alle anzeigen\"",
          "info": "Sichtbar, wenn die Kollektion mehr als die angezeigten Produkte enthält"
        },
        "header": {
          "content": "Produktkarte"
        },
        "image_ratio": {
          "label": "Bildverhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Porträt"
          },
          "options__3": {
            "label": "Quadrat"
          }
        },
        "show_secondary_image": {
          "label": "Hover-Effekt mit zweitem Bild"
        },
        "show_vendor": {
          "label": "Anbieter"
        },
        "show_rating": {
          "label": "Produktbewertung",
          "info": "Für Bewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "enable_quick_buy": {
          "label": "Schnelles Hinzufügen"
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "description": {
          "label": "Beschreibung"
        },
        "show_description": {
          "label": "Kollektionsbeschreibung im Admin-Panel anzeigen"
        },
        "description_style": {
          "label": "Beschreibungsstil",
          "options__1": {
            "label": "Nachricht"
          },
          "options__2": {
            "label": "Untertitel"
          },
          "options__3": {
            "label": "Großbuchstaben"
          }
        },
        "view_all_style": {
          "options__1": {
            "label": "Link"
          },
          "options__2": {
            "label": "Umriss-Schaltfläche"
          },
          "options__3": {
            "label": "Durchgehende Schaltfläche"
          },
          "label": "Stil \"Alles anzeigen\""
        },
        "enable_desktop_slider": {
          "label": "Karussell"
        },
        "full_width": {
          "label": "Produkte mit voller Breite"
        },
        "header_mobile": {
          "content": "Mobiles Layout"
        },
        "columns_mobile": {
          "label": "Spalten",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "swipe_on_mobile": {
          "label": "Karussell"
        },
        "header_text": {
          "content": "Text"
        },
        "header_collection": {
          "content": "Kollektions-Layout"
        }
      },
      "presets": {
        "name": "Vorgestellte Kollektion"
      }
    },
    "footer": {
      "name": "Fußzeile",
      "blocks": {
        "link_list": {
          "name": "Menü",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Quick-Links"
            },
            "menu": {
              "label": "Menü"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Titel"
            },
            "subtext": {
              "label": "Subtext",
              "default": "<p>Teile Kontaktinformationen, Shop-Details und Markeninhalte mit deinen Kunden.</p>"
            }
          }
        },
        "brand_information": {
          "name": "Markeninformationen",
          "settings": {
            "paragraph": {
              "content": "Markeninfos verwalten in [Theme-Einstellungen](/editor?context=theme&category=brand%20information)"
            },
            "show_social": {
              "label": "Social Media-Symbole",
              "info": "[Social-Media-Konten verwalten](/editor?context=theme&category=social%20media)"
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "E-Mail-Anmeldung"
        },
        "newsletter_heading": {
          "label": "Überschrift",
          "default": "Abonniere unsere E-Mails"
        },
        "header__1": {
          "content": "E-Mail-Anmeldung",
          "info": "Registrierungen hinzufügen [Kundenprofile](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "show_social": {
          "label": "Social Media-Symbole",
          "info": "[Social-Media-Konten verwalten](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Auswahl für Land/Region",
          "info": "[Länder/Regionen verwalten](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Sprachauswahl",
          "info": "[Sprachen verwalten](/admin/settings/languages)"
        },
        "payment_enable": {
          "label": "Symbole für Zahlungsmethode"
        },
        "margin_top": {
          "label": "Oberer Rand"
        },
        "show_policy": {
          "label": "Links zu Richtlinien",
          "info": "[Richtlinien verwalten](/admin/settings/legal)"
        },
        "header__9": {
          "content": "Versorgungsunternehmen"
        },
        "enable_follow_on_shop": {
          "label": "In Shop folgen",
          "info": "Shop Pay muss aktiviert sein. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        }
      }
    },
    "header": {
      "name": "Header",
      "settings": {
        "logo_position": {
          "label": "Logo-Position",
          "options__1": {
            "label": "Mitte links"
          },
          "options__2": {
            "label": "Oben links"
          },
          "options__3": {
            "label": "Oben zentriert"
          },
          "options__4": {
            "label": "Mitte zentriert"
          }
        },
        "menu": {
          "label": "Menü"
        },
        "show_line_separator": {
          "label": "Trennlinie"
        },
        "margin_bottom": {
          "label": "Unterer Rand"
        },
        "menu_type_desktop": {
          "label": "Menütyp",
          "options__1": {
            "label": "Dropdown"
          },
          "options__2": {
            "label": "Mega-Menü"
          },
          "options__3": {
            "label": "Einschub"
          }
        },
        "mobile_logo_position": {
          "label": "Logo-Position für mobile Darstellung",
          "options__1": {
            "label": "Zentriert"
          },
          "options__2": {
            "label": "Links"
          }
        },
        "logo_help": {
          "content": "Bearbeite dein Logo in den [Theme-Einstellungen](/editor?context=theme&category=logo)"
        },
        "sticky_header_type": {
          "label": "Fixierter Header",
          "options__1": {
            "label": "Keine"
          },
          "options__2": {
            "label": "Beim Hochscrollen"
          },
          "options__3": {
            "label": "Immer"
          },
          "options__4": {
            "label": "Immer, Größe des Logos reduzieren"
          }
        },
        "enable_country_selector": {
          "label": "Auswahl für Land/Region",
          "info": "[Länder/Regionen verwalten](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Sprachauswahl",
          "info": "[Sprachen verwalten](/admin/settings/languages)"
        },
        "header__1": {
          "content": "Farbe"
        },
        "menu_color_scheme": {
          "label": "Menü-Farbschema"
        },
        "enable_customer_avatar": {
          "label": "Avatar für Kundenkonto",
          "info": "Nur sichtbar, wenn Kunden mit Shop angemeldet sind. [Kundenkonten verwalten](/admin/settings/customer_accounts)"
        },
        "header__utilities": {
          "content": "Versorgungsunternehmen"
        }
      }
    },
    "image-banner": {
      "name": "Bild-Banner",
      "settings": {
        "image": {
          "label": "Bild 1"
        },
        "image_2": {
          "label": "Bild 2"
        },
        "stack_images_on_mobile": {
          "label": "Gestapelte Bilder"
        },
        "show_text_box": {
          "label": "Container"
        },
        "image_overlay_opacity": {
          "label": "Überlagerungsdeckkraft"
        },
        "show_text_below": {
          "label": "Container"
        },
        "image_height": {
          "label": "Höhe",
          "options__1": {
            "label": "An erstes Bild anpassen"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Mittel"
          },
          "options__4": {
            "label": "Groß"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Oben links"
          },
          "options__2": {
            "label": "Oben zentriert"
          },
          "options__3": {
            "label": "Oben rechts"
          },
          "options__4": {
            "label": "Mitte links"
          },
          "options__5": {
            "label": "Mitte zentriert"
          },
          "options__6": {
            "label": "Mitte rechts"
          },
          "options__7": {
            "label": "Unten links"
          },
          "options__8": {
            "label": "Unten zentriert"
          },
          "options__9": {
            "label": "Unten rechts"
          },
          "label": "Position"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung"
        },
        "mobile": {
          "content": "Mobiles Layout"
        },
        "content": {
          "content": "Inhalt"
        }
      },
      "blocks": {
        "heading": {
          "name": "Titel",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Bild-Banner"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Stelle Kunden Details zu Banner-Bildern oder Inhalt auf der Vorlage zur Verfügung."
            },
            "text_style": {
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              },
              "options__3": {
                "label": "Großbuchstaben"
              },
              "label": "Optik"
            }
          }
        },
        "buttons": {
          "name": "Schaltflächen",
          "settings": {
            "button_label_1": {
              "label": "Etikett",
              "info": "Zum Ausblenden leer lassen",
              "default": "Schaltflächenbeschriftung"
            },
            "button_link_1": {
              "label": "Link"
            },
            "button_style_secondary_1": {
              "label": "Umriss-Stil"
            },
            "button_label_2": {
              "label": "Etikett",
              "info": "Zum Ausblenden leer lassen",
              "default": "Schaltflächenbeschriftung"
            },
            "button_link_2": {
              "label": "Link"
            },
            "button_style_secondary_2": {
              "label": "Umriss-Stil"
            },
            "header_1": {
              "content": "Schaltfläche 1"
            },
            "header_2": {
              "content": "Schaltfläche 2"
            }
          }
        }
      },
      "presets": {
        "name": "Bild-Banner"
      }
    },
    "image-with-text": {
      "name": "Bild mit Text",
      "settings": {
        "image": {
          "label": "Bild"
        },
        "height": {
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Mittel"
          },
          "label": "Höhe",
          "options__4": {
            "label": "Groß"
          }
        },
        "layout": {
          "options__1": {
            "label": "Bild zuerst"
          },
          "options__2": {
            "label": "Zweites Bild"
          },
          "label": "Platzierung"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Klein"
          },
          "options__2": {
            "label": "Mittel"
          },
          "options__3": {
            "label": "Groß"
          },
          "label": "Breite"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Oben"
          },
          "options__2": {
            "label": "Mitte"
          },
          "options__3": {
            "label": "Unten"
          },
          "label": "Position"
        },
        "content_layout": {
          "options__1": {
            "label": "Keine Überlappung"
          },
          "options__2": {
            "label": "Überlappung"
          },
          "label": "Layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung Mobilgerät"
        },
        "header": {
          "content": "Inhalt"
        },
        "header_colors": {
          "content": "Farben"
        }
      },
      "blocks": {
        "heading": {
          "name": "Titel",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Bild mit Text"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "<p>Kombiniere Text mit einem Bild, um den Fokus auf dein Produkt, deine Kollektion oder deinen Blog-Beitrag zu richten. Du kannst außerdem weitere Details über die Verfügbarkeit oder den Stil und sogar eine Bewertung hinzufügen.</p>"
            },
            "text_style": {
              "label": "Optik",
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              }
            }
          }
        },
        "button": {
          "name": "Schaltfläche",
          "settings": {
            "button_label": {
              "label": "Etikett",
              "info": "Zum Ausblenden leer lassen",
              "default": "Schaltflächenbeschriftung"
            },
            "button_link": {
              "label": "Link"
            },
            "outline_button": {
              "label": "Umriss-Stil"
            }
          }
        },
        "caption": {
          "name": "Bildtext",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Tagline hinzufügen"
            },
            "text_style": {
              "label": "Optik",
              "options__1": {
                "label": "Untertitel"
              },
              "options__2": {
                "label": "Großbuchstaben"
              }
            },
            "caption_size": {
              "label": "Größe",
              "options__1": {
                "label": "Klein"
              },
              "options__2": {
                "label": "Mittel"
              },
              "options__3": {
                "label": "Groß"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Bild mit Text"
      }
    },
    "main-article": {
      "name": "Blog-Beitrag",
      "blocks": {
        "featured_image": {
          "name": "Feature-Bild",
          "settings": {
            "image_height": {
              "label": "Bildhöhe",
              "options__1": {
                "label": "An Bild anpassen"
              },
              "options__2": {
                "label": "Klein"
              },
              "options__3": {
                "label": "Mittel"
              },
              "options__4": {
                "label": "Groß"
              }
            }
          }
        },
        "title": {
          "name": "Titel",
          "settings": {
            "blog_show_date": {
              "label": "Datum"
            },
            "blog_show_author": {
              "label": "Autor"
            }
          }
        },
        "content": {
          "name": "Inhalt"
        },
        "share": {
          "name": "Teilen",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Teilen"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blog-Beiträge",
      "settings": {
        "show_image": {
          "label": "Feature-Bild"
        },
        "show_date": {
          "label": "Datum"
        },
        "show_author": {
          "label": "Autor"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Raster"
          },
          "options__2": {
            "label": "Collage"
          }
        },
        "image_height": {
          "label": "Bildhöhe",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Mittel"
          },
          "options__4": {
            "label": "Groß"
          }
        }
      }
    },
    "main-cart-footer": {
      "name": "Zwischensumme",
      "blocks": {
        "subtotal": {
          "name": "Zwischensumme"
        },
        "buttons": {
          "name": "Checkout-Schaltfläche"
        }
      }
    },
    "main-cart-items": {
      "name": "Artikel"
    },
    "main-collection-banner": {
      "name": "Kollektionsbanner",
      "settings": {
        "paragraph": {
          "content": "Kollektionsdetails werden [in deinem Admin-Panel verwaltet](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Beschreibung"
        },
        "show_collection_image": {
          "label": "Bild"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Produktraster",
      "settings": {
        "products_per_page": {
          "label": "Produkte pro Seite"
        },
        "enable_filtering": {
          "label": "Filter",
          "info": "Individualisiere Filter mit der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Sortierung"
        },
        "image_ratio": {
          "label": "Bildverhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Porträt"
          },
          "options__3": {
            "label": "Quadrat"
          }
        },
        "show_secondary_image": {
          "label": "Hover-Effekt mit zweitem Bild"
        },
        "show_vendor": {
          "label": "Anbieter"
        },
        "header__1": {
          "content": "Filtern und Sortieren"
        },
        "header__3": {
          "content": "Produktkarte"
        },
        "enable_tags": {
          "label": "Filter",
          "info": "Individualisiere Filter mit der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "show_rating": {
          "label": "Produktbewertung",
          "info": "Für Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "columns_mobile": {
          "label": "Spalten Mobilgeräte",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "filter_type": {
          "label": "Layout filtern",
          "options__1": {
            "label": "Horizontal"
          },
          "options__2": {
            "label": "Vertikal"
          },
          "options__3": {
            "label": "Einschub"
          }
        },
        "quick_add": {
          "label": "Schnelles Hinzufügen",
          "options": {
            "option_1": "Keine",
            "option_2": "Standard",
            "option_3": "Sammelaktion"
          }
        }
      }
    },
    "main-list-collections": {
      "name": "Listenseite für Kollektionen",
      "settings": {
        "title": {
          "label": "Überschrift",
          "default": "Kollektionen"
        },
        "sort": {
          "label": "Kollektionen sortieren",
          "options__1": {
            "label": "Alphabetisch, A-Z"
          },
          "options__2": {
            "label": "Alphabetisch, Z-A"
          },
          "options__3": {
            "label": "Datum, neu zu alt"
          },
          "options__4": {
            "label": "Datum, alt zu neu"
          },
          "options__5": {
            "label": "Produktanzahl, hoch zu niedrig"
          },
          "options__6": {
            "label": "Produktanzahl, niedrig zu hoch"
          }
        },
        "image_ratio": {
          "label": "Bildverhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Porträt"
          },
          "options__3": {
            "label": "Quadrat"
          }
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "header_mobile": {
          "content": "Mobiles Layout"
        },
        "columns_mobile": {
          "label": "Spalten Mobilgeräte",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "main-page": {
      "name": "Seite"
    },
    "main-password-footer": {
      "name": "Passwort-Fußzeile"
    },
    "main-password-header": {
      "name": "Passwort-Header",
      "settings": {
        "logo_help": {
          "content": "Bearbeite dein Logo in den [Theme-Einstellungen](/editor?context=theme&category=logo)"
        }
      }
    },
    "main-product": {
      "blocks": {
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Textblock"
            },
            "text_style": {
              "label": "Optik",
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              },
              "options__3": {
                "label": "Großbuchstaben"
              }
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Preis"
        },
        "quantity_selector": {
          "name": "Mengenauswahl"
        },
        "variant_picker": {
          "name": "Variantenauswahl",
          "settings": {
            "picker_type": {
              "label": "Optik",
              "options__1": {
                "label": "Dropdown"
              },
              "options__2": {
                "label": "Kapseln"
              }
            },
            "swatch_shape": {
              "label": "Farbfeld",
              "info": "Mehr Informationen zu [Farbfelder ](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) für Produktoptionen",
              "options__1": {
                "label": "Kreis"
              },
              "options__2": {
                "label": "Quadrat"
              },
              "options__3": {
                "label": "Keine"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Buy Buttons",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynamische Checkout-Buttons",
              "info": "Kunden wird die bevorzugte Zahlungsmethode angezeigt. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            },
            "show_gift_card_recipient": {
              "label": " Optionen für Gutscheinversand",
              "info": "Kunden können eine persönliche Nachricht hinzufügen und den Versand planen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"
            }
          }
        },
        "pickup_availability": {
          "name": "Verfügbarkeit von Abholungen"
        },
        "description": {
          "name": "Beschreibung"
        },
        "share": {
          "name": "Teilen",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Teilen"
            }
          }
        },
        "collapsible_tab": {
          "name": "Einklappbare Reihe",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Einklappbare Reihe"
            },
            "content": {
              "label": "Reiheninhalt"
            },
            "page": {
              "label": "Reiheninhalt der Seite"
            },
            "icon": {
              "options__1": {
                "label": "Ohne"
              },
              "options__2": {
                "label": "Apfel"
              },
              "options__3": {
                "label": "Banane"
              },
              "options__4": {
                "label": "Flasche"
              },
              "options__5": {
                "label": "Box"
              },
              "options__6": {
                "label": "Möhre"
              },
              "options__7": {
                "label": "Chat-Blase"
              },
              "options__8": {
                "label": "Häkchen"
              },
              "options__9": {
                "label": "Klemmbrett"
              },
              "options__10": {
                "label": "Milch"
              },
              "options__11": {
                "label": "Laktosefrei"
              },
              "options__12": {
                "label": "Trockner"
              },
              "options__13": {
                "label": "Auge"
              },
              "options__14": {
                "label": "Feuer"
              },
              "options__15": {
                "label": "Glutenfrei"
              },
              "options__16": {
                "label": "Herz"
              },
              "options__17": {
                "label": "Bügeleisen"
              },
              "options__18": {
                "label": "Blatt"
              },
              "options__19": {
                "label": "Leder"
              },
              "options__20": {
                "label": "Blitz"
              },
              "options__21": {
                "label": "Lippenstift"
              },
              "options__22": {
                "label": "Schloss"
              },
              "options__23": {
                "label": "Pinnnadel"
              },
              "options__24": {
                "label": "Ohne Nüsse"
              },
              "label": "Symbol",
              "options__25": {
                "label": "Hosen"
              },
              "options__26": {
                "label": "Pfotenabdruck"
              },
              "options__27": {
                "label": "Pfeffer"
              },
              "options__28": {
                "label": "Parfüm"
              },
              "options__29": {
                "label": "Flugzeug"
              },
              "options__30": {
                "label": "Pflanze"
              },
              "options__31": {
                "label": "Preisschild"
              },
              "options__32": {
                "label": "Fragezeichen"
              },
              "options__33": {
                "label": "Recyclen"
              },
              "options__34": {
                "label": "Rückgabe"
              },
              "options__35": {
                "label": "Lineal"
              },
              "options__36": {
                "label": "Servierteller"
              },
              "options__37": {
                "label": "Hemd"
              },
              "options__38": {
                "label": "Schuh"
              },
              "options__39": {
                "label": "Silhouette"
              },
              "options__40": {
                "label": "Schneeflocke"
              },
              "options__41": {
                "label": "Stern"
              },
              "options__42": {
                "label": "Stoppuhr"
              },
              "options__43": {
                "label": "Lieferwagen"
              },
              "options__44": {
                "label": "Wäsche"
              }
            }
          }
        },
        "popup": {
          "name": "Pop-up",
          "settings": {
            "link_label": {
              "label": "Link-Label",
              "default": "Pop-up-Linktext"
            },
            "page": {
              "label": "Seite"
            }
          }
        },
        "rating": {
          "name": "Produktbewertung",
          "settings": {
            "paragraph": {
              "content": "Für Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Ergänzende Produkte",
          "settings": {
            "paragraph": {
              "content": "Verwalte ergänzende Produkte in der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Überschrift",
              "default": "Passt gut zu"
            },
            "make_collapsible_row": {
              "label": "Einklappbare Reihe"
            },
            "icon": {
              "info": "Angezeigt, wenn einklappbare Reihe ausgewählt ist"
            },
            "product_list_limit": {
              "label": "Produktanzahl"
            },
            "products_per_page": {
              "label": "Produkte pro Seite"
            },
            "pagination_style": {
              "label": "Seitennummerierung",
              "options": {
                "option_1": "Punkte",
                "option_2": "Zähler",
                "option_3": "Zahlen"
              }
            },
            "product_card": {
              "heading": "Produktkarte"
            },
            "image_ratio": {
              "label": "Bildverhältnis",
              "options": {
                "option_1": "Hochformat",
                "option_2": "Quadrat"
              }
            },
            "enable_quick_add": {
              "label": "Schnelles Hinzufügen"
            }
          }
        },
        "icon_with_text": {
          "name": "Symbol mit Text",
          "settings": {
            "layout": {
              "label": "Layout",
              "options__1": {
                "label": "Horizontal"
              },
              "options__2": {
                "label": "Vertikal"
              }
            },
            "heading": {
              "info": "Zum Ausblenden dieser Kombination leer lassen"
            },
            "icon_1": {
              "label": "Symbol"
            },
            "image_1": {
              "label": "Bild"
            },
            "heading_1": {
              "label": "Überschrift",
              "default": "Titel"
            },
            "icon_2": {
              "label": "Symbol"
            },
            "image_2": {
              "label": "Bild"
            },
            "heading_2": {
              "label": "Überschrift",
              "default": "Titel"
            },
            "icon_3": {
              "label": "Symbol"
            },
            "image_3": {
              "label": "Bild"
            },
            "heading_3": {
              "label": "Überschrift",
              "default": "Titel"
            },
            "pairing_1": {
              "label": "Kombination 1",
              "info": "Wähle für jede Kombination ein Symbol oder ein Bild aus"
            },
            "pairing_2": {
              "label": "Kombination 2"
            },
            "pairing_3": {
              "label": "Kombination 3"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              },
              "options__3": {
                "label": "Großbuchstaben"
              }
            }
          }
        },
        "inventory": {
          "name": "Inventarstatus",
          "settings": {
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              },
              "options__3": {
                "label": "Großbuchstaben"
              }
            },
            "inventory_threshold": {
              "label": "Geringer Inventarschwellenwert"
            },
            "show_inventory_quantity": {
              "label": "Inventarbestand"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Medien"
        },
        "enable_video_looping": {
          "label": "Video in Dauerschleife"
        },
        "enable_sticky_info": {
          "label": "Fixierter Inhalt"
        },
        "hide_variants": {
          "label": "Andere Variantmedien ausblenden, nachdem eine ausgewählt wurde."
        },
        "gallery_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Gestapelt"
          },
          "options__2": {
            "label": "2 Spalten"
          },
          "options__3": {
            "label": "Vorschaubilder"
          },
          "options__4": {
            "label": "Karussell mit Vorschaubildern"
          }
        },
        "media_size": {
          "label": "Breite",
          "options__1": {
            "label": "Klein"
          },
          "options__2": {
            "label": "Mittel"
          },
          "options__3": {
            "label": "Groß"
          }
        },
        "mobile_thumbnails": {
          "label": "Mobiles Layout",
          "options__1": {
            "label": "2 Spalten"
          },
          "options__2": {
            "label": "Vorschaubilder anzeigen"
          },
          "options__3": {
            "label": "Vorschaubilder ausblenden"
          }
        },
        "media_position": {
          "label": "Position",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Rechts"
          }
        },
        "image_zoom": {
          "label": "Zoom",
          "options__1": {
            "label": "Lightbox öffnen"
          },
          "options__2": {
            "label": "Klicken und mit der Maus darüber fahren"
          },
          "options__3": {
            "label": "Nicht zoomen"
          }
        },
        "constrain_to_viewport": {
          "label": "Auf Bildschirmhöhe beschränken"
        },
        "media_fit": {
          "label": "Passform",
          "options__1": {
            "label": "Original"
          },
          "options__2": {
            "label": "Füllung"
          }
        }
      },
      "name": "Produktinformationen"
    },
    "main-search": {
      "name": "Suchergebnisse",
      "settings": {
        "image_ratio": {
          "label": "Bildverhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Porträt"
          },
          "options__3": {
            "label": "Quadrat"
          }
        },
        "show_secondary_image": {
          "label": "Hover-Effekt mit zweitem Bild"
        },
        "show_vendor": {
          "label": "Anbieter"
        },
        "header__1": {
          "content": "Produktkarte"
        },
        "header__2": {
          "content": "Blog-Karte"
        },
        "article_show_date": {
          "label": "Datum"
        },
        "article_show_author": {
          "label": "Autor"
        },
        "show_rating": {
          "label": "Produktbewertung",
          "info": "Für Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "columns_mobile": {
          "label": "Spalten Mobilgeräte",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Mit mehreren Spalten",
      "settings": {
        "title": {
          "label": "Überschrift",
          "default": "Mit mehreren Spalten"
        },
        "image_width": {
          "label": "Breite",
          "options__1": {
            "label": "Drittelbreite der Spalte"
          },
          "options__2": {
            "label": "Halbe Breite der Spalte"
          },
          "options__3": {
            "label": "Ganze Breite der Spalte"
          }
        },
        "image_ratio": {
          "label": "Verhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Porträt"
          },
          "options__3": {
            "label": "Quadrat"
          },
          "options__4": {
            "label": "Kreis"
          }
        },
        "column_alignment": {
          "label": "Spaltenausrichtung",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          }
        },
        "background_style": {
          "label": "Sekundärer Hintergrund",
          "options__1": {
            "label": "Ohne"
          },
          "options__2": {
            "label": "Als Spaltenhintergrund anzeigen"
          }
        },
        "button_label": {
          "label": "Etikett",
          "default": "Schaltflächenbeschriftung",
          "info": "Zum Ausblenden leer lassen"
        },
        "button_link": {
          "label": "Link"
        },
        "swipe_on_mobile": {
          "label": "Karussell"
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "header_mobile": {
          "content": "Mobiles Layout"
        },
        "columns_mobile": {
          "label": "Spalten",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_text": {
          "content": "Überschrift"
        },
        "header_image": {
          "content": "Bild"
        },
        "header_layout": {
          "content": "Layout"
        },
        "header_button": {
          "content": "Schaltfläche"
        }
      },
      "blocks": {
        "column": {
          "name": "Spalte",
          "settings": {
            "image": {
              "label": "Bild"
            },
            "title": {
              "label": "Überschrift",
              "default": "Spalte"
            },
            "text": {
              "label": "Beschreibung",
              "default": "<p>Kombiniere Text mit einem Bild, um den Fokus auf dein Produkt, deine Kollektion oder deinen Blog-Beitrag zu richten. Du kannst außerdem weitere Details über die Verfügbarkeit oder den Stil und sogar eine Bewertung hinzufügen.</p>"
            },
            "link_label": {
              "label": "Link-Label",
              "info": "Zum Ausblenden leer lassen"
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Mit mehreren Spalten"
      }
    },
    "newsletter": {
      "name": "E-Mail-Anmeldung",
      "settings": {
        "full_width": {
          "label": "Volle Breite"
        },
        "paragraph": {
          "content": "Registrierungen hinzufügen [Kundenprofile](https://help.shopify.com/manual/customers/manage-customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Titel",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Abonniere unsere E-Mails"
            }
          }
        },
        "paragraph": {
          "name": "Text",
          "settings": {
            "paragraph": {
              "label": "Text",
              "default": "<p>Erfahre als Erster von neuen Kollektionen und exklusiven Angeboten.</p>"
            }
          }
        },
        "email_form": {
          "name": "E-Mail-Formular"
        }
      },
      "presets": {
        "name": "E-Mail-Anmeldung"
      }
    },
    "page": {
      "name": "Seite",
      "settings": {
        "page": {
          "label": "Seite"
        }
      },
      "presets": {
        "name": "Seite"
      }
    },
    "rich-text": {
      "name": "Rich Text",
      "settings": {
        "full_width": {
          "label": "Volle Breite"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Inhaltsposition"
        },
        "content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Inhaltsausrichtung"
        }
      },
      "blocks": {
        "heading": {
          "name": "Titel",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Erzähle etwas über deine Marke"
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "<p>Teile Infos über deine Marke mit deinen Kunden. Beschreibe ein Produkt, kündige etwas an oder heiße Kunden willkommen.</p>"
            }
          }
        },
        "buttons": {
          "name": "Schaltflächen",
          "settings": {
            "button_label_1": {
              "label": "Etikett",
              "info": "Zum Ausblenden leer lassen",
              "default": "Schaltflächenbeschriftung"
            },
            "button_link_1": {
              "label": "Link"
            },
            "button_style_secondary_1": {
              "label": "Umriss-Stil"
            },
            "button_label_2": {
              "label": "Etikett",
              "info": "Leer lassen, um Etikett auszublenden"
            },
            "button_link_2": {
              "label": "Link"
            },
            "button_style_secondary_2": {
              "label": "Umriss-Stil"
            },
            "header_button1": {
              "content": "Schaltfläche 1"
            },
            "header_button2": {
              "content": "Schaltfläche 2"
            }
          }
        },
        "caption": {
          "name": "Bildtext",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Tagline hinzufügen"
            },
            "text_style": {
              "label": "Optik",
              "options__1": {
                "label": "Untertitel"
              },
              "options__2": {
                "label": "Großbuchstaben"
              }
            },
            "caption_size": {
              "label": "Größe",
              "options__1": {
                "label": "Klein"
              },
              "options__2": {
                "label": "Mittel"
              },
              "options__3": {
                "label": "Groß"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Rich Text"
      }
    },
    "apps": {
      "name": "Apps",
      "settings": {
        "include_margins": {
          "label": "Abschnittränder so gestalten wie das Theme"
        }
      },
      "presets": {
        "name": "Apps"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Titel",
          "default": "Video"
        },
        "cover_image": {
          "label": "Titelbild"
        },
        "video_url": {
          "label": "URL",
          "info": "YouTube- oder Vimeo-URL verwenden"
        },
        "description": {
          "label": "Video-Alt-Text",
          "info": "Beschreibe das Video für Nutzer von Bildschirmlesegeräten"
        },
        "image_padding": {
          "label": "Bild-Padding hinzufügen",
          "info": "Wähle Bild-Padding aus, wenn du nicht möchtest, dass dein Titelbild abgeschnitten wird."
        },
        "full_width": {
          "label": "Volle Breite"
        },
        "video": {
          "label": "Video"
        },
        "enable_video_looping": {
          "label": "Video in Dauerschleife"
        },
        "header__1": {
          "content": "Von Shopify gehostetes Video"
        },
        "header__2": {
          "content": "Oder Video von URL einbetten"
        },
        "header__3": {
          "content": "Layout"
        },
        "paragraph": {
          "content": "Wird angezeigt, wenn kein von Shopify gehostetes Video ausgewählt wurde"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Vorgestelltes Produkt",
      "blocks": {
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text",
              "default": "Textblock"
            },
            "text_style": {
              "label": "Optik",
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              },
              "options__3": {
                "label": "Großbuchstaben"
              }
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "price": {
          "name": "Preis"
        },
        "quantity_selector": {
          "name": "Mengenauswahl"
        },
        "variant_picker": {
          "name": "Variantenauswahl",
          "settings": {
            "picker_type": {
              "label": "Optik",
              "options__1": {
                "label": "Dropdown"
              },
              "options__2": {
                "label": "Kapseln"
              }
            },
            "swatch_shape": {
              "label": "Farbfeld",
              "info": "Mehr Informationen zu [Farbfelder ](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) für Produktoptionen",
              "options__1": {
                "label": "Kreis"
              },
              "options__2": {
                "label": "Quadrat"
              },
              "options__3": {
                "label": "Keine"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Buy Buttons",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynamische Checkout-Buttons",
              "info": "Kunden wird die bevorzugte Zahlungsmethode angezeigt. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Beschreibung"
        },
        "share": {
          "name": "Teilen",
          "settings": {
            "featured_image_info": {
              "content": "Wenn du einen Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Text",
              "default": "Teilen"
            }
          }
        },
        "rating": {
          "name": "Produktbewertung",
          "settings": {
            "paragraph": {
              "content": "Für Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Textstil",
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              },
              "options__3": {
                "label": "Großbuchstaben"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Produkt"
        },
        "secondary_background": {
          "label": "Sekundärer Hintergrund"
        },
        "header": {
          "content": "Medien"
        },
        "enable_video_looping": {
          "label": "Video in Dauerschleife"
        },
        "hide_variants": {
          "label": "Medien von nicht ausgewählten Varianten auf dem Desktop ausblenden"
        },
        "media_position": {
          "label": "Position",
          "info": "Positionen werden automatisch für die mobile Nutzung optimiert.",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Rechts"
          }
        }
      },
      "presets": {
        "name": "Vorgestelltes Produkt"
      }
    },
    "email-signup-banner": {
      "name": "Banner für E-Mail-Anmeldung",
      "settings": {
        "paragraph": {
          "content": "Registrierungen hinzufügen [Kundenprofile](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "image": {
          "label": "Hintergrundbild"
        },
        "show_background_image": {
          "label": "Hintergrundbild anzeigen"
        },
        "show_text_box": {
          "label": "Container"
        },
        "image_overlay_opacity": {
          "label": "Überlagerungsdeckkraft"
        },
        "show_text_below": {
          "label": "Text unter Bild stapeln"
        },
        "image_height": {
          "label": "Höhe",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Mittel"
          },
          "options__4": {
            "label": "Groß"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Oben links"
          },
          "options__2": {
            "label": "Oben zentriert"
          },
          "options__3": {
            "label": "Oben rechts"
          },
          "options__4": {
            "label": "Mitte links"
          },
          "options__5": {
            "label": "Mitte zentriert"
          },
          "options__6": {
            "label": "Mitte rechts"
          },
          "options__7": {
            "label": "Unten links"
          },
          "options__8": {
            "label": "Unten zentriert"
          },
          "options__9": {
            "label": "Unten rechts"
          },
          "label": "Position"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung"
        },
        "header": {
          "content": "Mobiles Layout"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung"
        },
        "color_scheme": {
          "info": "Sichtbar, wenn Container angezeigt wird."
        },
        "content_header": {
          "content": "Inhalt"
        }
      },
      "blocks": {
        "heading": {
          "name": "Titel",
          "settings": {
            "heading": {
              "label": "Titel",
              "default": "Eröffnet demnächst"
            }
          }
        },
        "paragraph": {
          "name": "Text",
          "settings": {
            "paragraph": {
              "label": "Text",
              "default": "<p>Erfahre als Erster von unserem Launch.</p>"
            },
            "text_style": {
              "options__1": {
                "label": "Nachricht"
              },
              "options__2": {
                "label": "Untertitel"
              },
              "label": "Optik"
            }
          }
        },
        "email_form": {
          "name": "E-Mail-Formular"
        }
      },
      "presets": {
        "name": "Banner für E-Mail-Anmeldung"
      }
    },
    "slideshow": {
      "name": "Slideshow",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Volle Breite"
          },
          "options__2": {
            "label": "Seite"
          }
        },
        "slide_height": {
          "label": "Höhe",
          "options__1": {
            "label": "An erstes Bild anpassen"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Mittel"
          },
          "options__4": {
            "label": "Groß"
          }
        },
        "slider_visual": {
          "label": "Seitennummerierung",
          "options__1": {
            "label": "Zähler"
          },
          "options__2": {
            "label": "Punkte"
          },
          "options__3": {
            "label": "Zahlen"
          }
        },
        "auto_rotate": {
          "label": "Autorotieren der Slides"
        },
        "change_slides_speed": {
          "label": "Anzeige der nächsten Folie alle"
        },
        "show_text_below": {
          "label": "Text unter Bild stapeln"
        },
        "mobile": {
          "content": "Mobiles Layout"
        },
        "accessibility": {
          "content": "Barrierefreiheit",
          "label": "Slideshow-Beschreibung",
          "info": "Beschreibe die Slideshow für Nutzer von Bildschirmlesegeräten",
          "default": "Slideshow zu deiner Marke"
        }
      },
      "blocks": {
        "slide": {
          "name": "Folie",
          "settings": {
            "image": {
              "label": "Bild"
            },
            "heading": {
              "label": "Titel",
              "default": "Slideshow"
            },
            "subheading": {
              "label": "Unter-Überschrift",
              "default": "Erzähle deine Geschichte mit Fotos"
            },
            "button_label": {
              "label": "Etikett",
              "info": "Zum Ausblenden leer lassen",
              "default": "Schaltflächenbeschriftung"
            },
            "link": {
              "label": "Link"
            },
            "secondary_style": {
              "label": "Umriss-Stil"
            },
            "box_align": {
              "label": "Inhaltsposition",
              "options__1": {
                "label": "Oben links"
              },
              "options__2": {
                "label": "Oben zentriert"
              },
              "options__3": {
                "label": "Oben rechts"
              },
              "options__4": {
                "label": "Mitte links"
              },
              "options__5": {
                "label": "Mitte mittig"
              },
              "options__6": {
                "label": "Mitte rechts"
              },
              "options__7": {
                "label": "Unten links"
              },
              "options__8": {
                "label": "Unten mittig"
              },
              "options__9": {
                "label": "Unten rechts"
              }
            },
            "show_text_box": {
              "label": "Container"
            },
            "text_alignment": {
              "label": "Inhaltsausrichtung",
              "option_1": {
                "label": "Links"
              },
              "option_2": {
                "label": "Zentriert"
              },
              "option_3": {
                "label": "Rechts"
              }
            },
            "image_overlay_opacity": {
              "label": "Überlagerungsdeckkraft"
            },
            "text_alignment_mobile": {
              "label": "Mobile Inhaltsausrichtung",
              "options__1": {
                "label": "Links"
              },
              "options__2": {
                "label": "Zentriert"
              },
              "options__3": {
                "label": "Rechts"
              }
            },
            "header_button": {
              "content": "Schaltfläche"
            },
            "header_layout": {
              "content": "Layout"
            },
            "header_text": {
              "content": "Text"
            },
            "header_colors": {
              "content": "Farben"
            }
          }
        }
      },
      "presets": {
        "name": "Slideshow"
      }
    },
    "collapsible_content": {
      "name": "Einklappbarer Inhalt",
      "settings": {
        "caption": {
          "label": "Bildtext"
        },
        "heading": {
          "label": "Überschrift",
          "default": "Einklappbarer Inhalt"
        },
        "heading_alignment": {
          "label": "Ausrichtung der Überschrift",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          }
        },
        "layout": {
          "label": "Container",
          "options__1": {
            "label": "Kein Container"
          },
          "options__2": {
            "label": "Reihencontainer"
          },
          "options__3": {
            "label": "Abschnittscontainer"
          }
        },
        "open_first_collapsible_row": {
          "label": "Erste Reihe öffnen"
        },
        "header": {
          "content": "Bild"
        },
        "image": {
          "label": "Bild"
        },
        "image_ratio": {
          "label": "Bildverhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Groß"
          }
        },
        "desktop_layout": {
          "label": "Platzierung",
          "options__1": {
            "label": "Bild zuerst"
          },
          "options__2": {
            "label": "Bild an zweiter Stelle"
          }
        },
        "container_color_scheme": {
          "label": "Farbschema für Container"
        },
        "layout_header": {
          "content": "Layout"
        },
        "section_color_scheme": {
          "label": "Farbschema des Abschnitts"
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Einklappbare Reihe",
          "settings": {
            "heading": {
              "label": "Überschrift",
              "default": "Einklappbare Reihe"
            },
            "row_content": {
              "label": "Reiheninhalt"
            },
            "page": {
              "label": "Reiheninhalt der Seite"
            },
            "icon": {
              "label": "Symbol",
              "options__1": {
                "label": "Keine"
              },
              "options__2": {
                "label": "Apfel"
              },
              "options__3": {
                "label": "Banane"
              },
              "options__4": {
                "label": "Flasche"
              },
              "options__5": {
                "label": "Box"
              },
              "options__6": {
                "label": "Möhre"
              },
              "options__7": {
                "label": "Chat-Blase"
              },
              "options__8": {
                "label": "Häkchen"
              },
              "options__9": {
                "label": "Klemmbrett"
              },
              "options__10": {
                "label": "Milch"
              },
              "options__11": {
                "label": "Laktosefrei"
              },
              "options__12": {
                "label": "Trockner"
              },
              "options__13": {
                "label": "Auge"
              },
              "options__14": {
                "label": "Feuer"
              },
              "options__15": {
                "label": "Glutenfrei"
              },
              "options__16": {
                "label": "Herz"
              },
              "options__17": {
                "label": "Bügeleisen"
              },
              "options__18": {
                "label": "Blatt"
              },
              "options__19": {
                "label": "Leder"
              },
              "options__20": {
                "label": "Blitz"
              },
              "options__21": {
                "label": "Lippenstift"
              },
              "options__22": {
                "label": "Schloss"
              },
              "options__23": {
                "label": "Pinnnadel"
              },
              "options__24": {
                "label": "Ohne Nüsse"
              },
              "options__25": {
                "label": "Hosen"
              },
              "options__26": {
                "label": "Pfotenabdruck"
              },
              "options__27": {
                "label": "Pfeffer"
              },
              "options__28": {
                "label": "Parfüm"
              },
              "options__29": {
                "label": "Flugzeug"
              },
              "options__30": {
                "label": "Pflanze"
              },
              "options__31": {
                "label": "Preisschild"
              },
              "options__32": {
                "label": "Fragezeichen"
              },
              "options__33": {
                "label": "Recyclen"
              },
              "options__34": {
                "label": "Rückgabe"
              },
              "options__35": {
                "label": "Lineal"
              },
              "options__36": {
                "label": "Servierteller"
              },
              "options__37": {
                "label": "Hemd"
              },
              "options__38": {
                "label": "Schuh"
              },
              "options__39": {
                "label": "Silhouette"
              },
              "options__40": {
                "label": "Schneeflocke"
              },
              "options__41": {
                "label": "Stern"
              },
              "options__42": {
                "label": "Stoppuhr"
              },
              "options__43": {
                "label": "Lieferwagen"
              },
              "options__44": {
                "label": "Wäsche"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Einklappbarer Inhalt"
      }
    },
    "main-account": {
      "name": "Konto"
    },
    "main-activate-account": {
      "name": "Kontoaktivierung"
    },
    "main-addresses": {
      "name": "Adressen"
    },
    "main-login": {
      "name": "Login",
      "shop_login_button": {
        "enable": "\"Mit Shop anmelden\" aktivieren"
      }
    },
    "main-order": {
      "name": "Bestellung"
    },
    "main-register": {
      "name": "Registrierung"
    },
    "main-reset-password": {
      "name": "Passwort zurücksetzen"
    },
    "related-products": {
      "name": "Ähnliche Produkte",
      "settings": {
        "heading": {
          "label": "Überschrift"
        },
        "products_to_show": {
          "label": "Produktanzahl"
        },
        "columns_desktop": {
          "label": "Spalten"
        },
        "paragraph__1": {
          "content": "Ergänzende Produkte können in der [Search & Discovery-App](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations) verwaltet werden",
          "default": "Das könnte dir auch gefallen"
        },
        "header__2": {
          "content": "Produktkarte"
        },
        "image_ratio": {
          "label": "Bildverhältnis",
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Hochformat"
          },
          "options__3": {
            "label": "Quadrat"
          }
        },
        "show_secondary_image": {
          "label": "Hover-Effekt mit zweitem Bild"
        },
        "show_vendor": {
          "label": "Anbieter"
        },
        "show_rating": {
          "label": "Produktbewertung",
          "info": "Für Produktbewertungen wird eine App benötigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"
        },
        "columns_mobile": {
          "label": "Spalten Mobilgeräte",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multirow": {
      "name": "Mehrreihig",
      "settings": {
        "image": {
          "label": "Bild"
        },
        "image_height": {
          "options__1": {
            "label": "An Bild anpassen"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Mittel"
          },
          "options__4": {
            "label": "Groß"
          },
          "label": "Höhe"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Klein"
          },
          "options__2": {
            "label": "Mittel"
          },
          "options__3": {
            "label": "Groß"
          },
          "label": "Breite"
        },
        "text_style": {
          "options__1": {
            "label": "Nachricht"
          },
          "options__2": {
            "label": "Untertitel"
          },
          "label": "Textstil"
        },
        "button_style": {
          "options__1": {
            "label": "Durchgehende Schaltfläche"
          },
          "options__2": {
            "label": "Umriss-Schaltfläche"
          },
          "label": "Schaltflächenstil"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Oben"
          },
          "options__2": {
            "label": "Mitte"
          },
          "options__3": {
            "label": "Unten"
          },
          "label": "Position"
        },
        "image_layout": {
          "options__1": {
            "label": "Abwechselnd von links"
          },
          "options__2": {
            "label": "Abwechselnd von rechts"
          },
          "options__3": {
            "label": "Linksbündig"
          },
          "options__4": {
            "label": "Rechtsbündig"
          },
          "label": "Platzierung"
        },
        "container_color_scheme": {
          "label": "Farbschema für Container"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Zentriert"
          },
          "options__3": {
            "label": "Rechts"
          },
          "label": "Ausrichtung Mobilgerät"
        },
        "header": {
          "content": "Bild"
        },
        "header_2": {
          "content": "Inhalt"
        },
        "header_3": {
          "content": "Farben"
        }
      },
      "blocks": {
        "row": {
          "name": "Reihe",
          "settings": {
            "image": {
              "label": "Bild"
            },
            "caption": {
              "label": "Bildtext",
              "default": "Bildtext"
            },
            "heading": {
              "label": "Überschrift",
              "default": "Reihe"
            },
            "text": {
              "label": "Text",
              "default": "<p>Kombiniere Text mit einem Bild, um den Fokus auf dein Produkt, deine Kollektion oder deinen Blog-Beitrag zu richten. Du kannst außerdem weitere Details über die Verfügbarkeit oder den Stil und sogar eine Bewertung hinzufügen.</p>"
            },
            "button_label": {
              "label": "Schaltflächenbeschriftung",
              "default": "Schaltflächenbeschriftung",
              "info": "Zum Ausblenden leer lassen"
            },
            "button_link": {
              "label": "Schaltflächenlink"
            }
          }
        }
      },
      "presets": {
        "name": "Mehrreihig"
      }
    },
    "quick-order-list": {
      "name": "Schnelle Bestellliste",
      "settings": {
        "show_image": {
          "label": "Bilder"
        },
        "show_sku": {
          "label": "SKUs"
        },
        "variants_per_page": {
          "label": "Varianten pro Seite"
        }
      },
      "presets": {
        "name": "Schnelle Bestellliste"
      }
    }
  }
}
