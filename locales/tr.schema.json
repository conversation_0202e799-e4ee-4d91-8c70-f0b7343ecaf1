/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Renkler",
      "settings": {
        "background": {
          "label": "Arka plan"
        },
        "background_gradient": {
          "label": "Arka plan gradyanı",
          "info": "Arka plan gradyanı, mümkün olduğunda arka planın yerine geçer."
        },
        "text": {
          "label": "Metin"
        },
        "button_background": {
          "label": "Sabit düğme arka planı"
        },
        "button_label": {
          "label": "Sabit düğme etiketi"
        },
        "secondary_button_label": {
          "label": "Dış çizgi düğmesi"
        },
        "shadow": {
          "label": "Gölge"
        }
      }
    },
    "typography": {
      "name": "Tipografi",
      "settings": {
        "type_header_font": {
          "label": "Yazı tipi"
        },
        "header__1": {
          "content": "Başlıklar"
        },
        "header__2": {
          "content": "Gövde"
        },
        "type_body_font": {
          "label": "Yazı tipi"
        },
        "heading_scale": {
          "label": "Ölçek"
        },
        "body_scale": {
          "label": "Ölçek"
        }
      }
    },
    "social-media": {
      "name": "Sosyal medya",
      "settings": {
        "social_twitter_link": {
          "label": "X/Twitter",
          "info": "https://x.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "http://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "https://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Sosyal medya hesapları"
        }
      }
    },
    "currency_format": {
      "name": "Para birimi biçimi",
      "settings": {
        "currency_code_enabled": {
          "label": "Para birimi kodları"
        },
        "paragraph": "Sepet ve ödeme ücretleri her zaman para birimi kodlarını gösterir"
      }
    },
    "layout": {
      "name": "Düzen",
      "settings": {
        "page_width": {
          "label": "Sayfa genişliği"
        },
        "spacing_sections": {
          "label": "Şablon bölümleri arasındaki alan"
        },
        "header__grid": {
          "content": "Izgara"
        },
        "paragraph__grid": {
          "content": "Birden fazla sütun veya satır içeren alanları etkiler"
        },
        "spacing_grid_horizontal": {
          "label": "Yatay boşluk"
        },
        "spacing_grid_vertical": {
          "label": "Dikey boşluk"
        }
      }
    },
    "search_input": {
      "name": "Arama davranışı",
      "settings": {
        "predictive_search_enabled": {
          "label": "Arama önerileri"
        },
        "predictive_search_show_vendor": {
          "label": "Ürün satıcısı",
          "info": "Arama önerileri etkin durumdayken gösterilir"
        },
        "predictive_search_show_price": {
          "label": "Ürün fiyatı",
          "info": "Arama önerileri etkin durumdayken gösterilir"
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Kenarlık"
        },
        "header__shadow": {
          "content": "Gölge"
        },
        "blur": {
          "label": "Bulanıklık"
        },
        "corner_radius": {
          "label": "Köşe yarıçapı"
        },
        "horizontal_offset": {
          "label": "Yatay dengeleme"
        },
        "vertical_offset": {
          "label": "Dikey dengeleme"
        },
        "thickness": {
          "label": "Kalınlık"
        },
        "opacity": {
          "label": "Opaklık"
        },
        "image_padding": {
          "label": "Görsel dolgusu"
        },
        "text_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Metin hizalaması"
        }
      }
    },
    "badges": {
      "name": "Rozetler",
      "settings": {
        "position": {
          "options__1": {
            "label": "Alt sol"
          },
          "options__2": {
            "label": "Alt sağ"
          },
          "options__3": {
            "label": "Üst sol"
          },
          "options__4": {
            "label": "Üst sağ"
          },
          "label": "Kartlardaki konum"
        },
        "sale_badge_color_scheme": {
          "label": "İndirim rozeti renk şeması"
        },
        "sold_out_badge_color_scheme": {
          "label": "Tükendi rozeti renk şeması"
        }
      }
    },
    "buttons": {
      "name": "Düğmeler"
    },
    "variant_pills": {
      "name": "Varyasyon seçenekleri",
      "paragraph": "Varyasyon seçenekleri, [ürün varyasyonlarınızı](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block) göstermenin bir yoludur"
    },
    "inputs": {
      "name": "Girdiler"
    },
    "content_containers": {
      "name": "İçerik kapsayıcıları"
    },
    "popups": {
      "name": "Açılır menüler ve pencereler",
      "paragraph": "Gezinme açılır menüleri, açılır pencere modları ve sepet açılır pencereleri gibi alanları etkiler"
    },
    "media": {
      "name": "Medya"
    },
    "drawers": {
      "name": "Çekmeceler"
    },
    "cart": {
      "name": "Sepet",
      "settings": {
        "cart_type": {
          "label": "Tür",
          "drawer": {
            "label": "Çekmece"
          },
          "page": {
            "label": "Sayfa"
          },
          "notification": {
            "label": "Açılır pencere bildirimi"
          }
        },
        "show_vendor": {
          "label": "Satıcı"
        },
        "show_cart_note": {
          "label": "Sepet notu"
        },
        "cart_drawer": {
          "header": "Sepet çekmecesi",
          "collection": {
            "label": "Koleksiyon",
            "info": "Sepet çekmecesi boş olduğunda gösterilir"
          }
        }
      }
    },
    "cards": {
      "name": "Ürün kartları",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standart"
          },
          "options__2": {
            "label": "Kart"
          },
          "label": "Stil"
        }
      }
    },
    "collection_cards": {
      "name": "Koleksiyon kartları",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standart"
          },
          "options__2": {
            "label": "Kart"
          },
          "label": "Stil"
        }
      }
    },
    "blog_cards": {
      "name": "Blog kartları",
      "settings": {
        "style": {
          "options__1": {
            "label": "Standart"
          },
          "options__2": {
            "label": "Kart"
          },
          "label": "Stil"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Genişlik"
        },
        "favicon": {
          "label": "Favicon",
          "info": "32 x 32 pikselde görüntüleniyor"
        }
      }
    },
    "brand_information": {
      "name": "Marka bilgileri",
      "settings": {
        "brand_headline": {
          "label": "Başlık"
        },
        "brand_description": {
          "label": "Açıklama"
        },
        "brand_image": {
          "label": "Görsel"
        },
        "brand_image_width": {
          "label": "Görsel genişliği"
        },
        "paragraph": {
          "content": "Altbilginin marka bilgileri bloğunda görüntülenir"
        }
      }
    },
    "animations": {
      "name": "Animasyonlar",
      "settings": {
        "animations_reveal_on_scroll": {
          "label": "Kaydırırken bölümleri göster"
        },
        "animations_hover_elements": {
          "options__1": {
            "label": "Yok"
          },
          "options__2": {
            "label": "Dikey lift"
          },
          "label": "Üzerine gelme efekti",
          "info": "Kartları ve düğmeleri etkiler",
          "options__3": {
            "label": "3D kaldırma"
          }
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Dolgu",
        "padding_top": "Üst",
        "padding_bottom": "Alt"
      },
      "spacing": "Boşluk",
      "colors": {
        "label": "Renk şeması",
        "has_cards_info": "Kart renk şemasını değiştirmek için tema ayarlarınızı güncelleyin."
      },
      "heading_size": {
        "label": "Başlık boyutu",
        "options__1": {
          "label": "Küçük"
        },
        "options__2": {
          "label": "Orta"
        },
        "options__3": {
          "label": "Büyük"
        },
        "options__4": {
          "label": "Çok büyük"
        },
        "options__5": {
          "label": "Çok çok büyük"
        }
      },
      "image_shape": {
        "options__1": {
          "label": "Varsayılan"
        },
        "options__2": {
          "label": "Kemer"
        },
        "options__3": {
          "label": "Leke"
        },
        "options__4": {
          "label": "Sola ok"
        },
        "options__5": {
          "label": "Sağa ok"
        },
        "options__6": {
          "label": "Baklava"
        },
        "options__7": {
          "label": "Paralelkenar"
        },
        "options__8": {
          "label": "Yuvarlak"
        },
        "label": "Görsel şekli"
      },
      "animation": {
        "content": "Animasyonlar",
        "image_behavior": {
          "options__1": {
            "label": "Yok"
          },
          "options__2": {
            "label": "Ortam içinde hareket"
          },
          "label": "Animasyon",
          "options__3": {
            "label": "Sabit arka plan konumu"
          },
          "options__4": {
            "label": "Kaydırarak yakınlaştır"
          }
        }
      }
    },
    "announcement-bar": {
      "name": "Duyuru çubuğu",
      "blocks": {
        "announcement": {
          "name": "Duyuru",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "Mağazamıza hoş geldiniz"
            },
            "text_alignment": {
              "label": "Metin hizalaması",
              "options__1": {
                "label": "Sol"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Sağ"
              }
            },
            "link": {
              "label": "Bağlantı"
            }
          }
        }
      },
      "settings": {
        "auto_rotate": {
          "label": "Duyuruları otomatik olarak döndür"
        },
        "change_slides_speed": {
          "label": "Şu zaman aralığında değiştir:"
        },
        "show_social": {
          "label": "Sosyal medya simgeleri",
          "info": "[Sosyal medya hesaplarını yönet](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Ülke/bölge seçici",
          "info": "[Ülkeleri/bölgeleri yönet](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Dil seçici",
          "info": "[Dilleri yönet](/admin/settings/languages)"
        },
        "heading_utilities": {
          "content": "Yardımcı araçlar"
        },
        "paragraph": {
          "content": "Yalnızca geniş ekranlarda göster"
        }
      },
      "presets": {
        "name": "Duyuru çubuğu"
      }
    },
    "collage": {
      "name": "Kolaj",
      "settings": {
        "heading": {
          "label": "Başlık",
          "default": "Multimedya kolajı"
        },
        "desktop_layout": {
          "label": "Düzen",
          "options__1": {
            "label": "İlk büyük blok"
          },
          "options__2": {
            "label": "Son büyük blok"
          }
        },
        "mobile_layout": {
          "label": "Mobil düzen",
          "options__1": {
            "label": "Kolaj"
          },
          "options__2": {
            "label": "Sütun"
          }
        },
        "card_styles": {
          "label": "Kart stili",
          "info": "Bireysel kart stillerini [tema ayarları](/editor?context=theme&category=product%20cards) bölümünde yönetin",
          "options__1": {
            "label": "Bireysel kart stilleri kullanın"
          },
          "options__2": {
            "label": "Hepsinin stilini ürün kartı şeklinde ayarla"
          }
        },
        "header_layout": {
          "content": "Düzen"
        }
      },
      "blocks": {
        "image": {
          "name": "Image",
          "settings": {
            "image": {
              "label": "Görsel"
            }
          }
        },
        "product": {
          "name": "Ürün",
          "settings": {
            "product": {
              "label": "Ürün"
            },
            "secondary_background": {
              "label": "İkincil arka planı göster"
            },
            "second_image": {
              "label": "Üstüne gelindiğinde ikinci görseli göster"
            }
          }
        },
        "collection": {
          "name": "Koleksiyon",
          "settings": {
            "collection": {
              "label": "Koleksiyon"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Kapak görseli"
            },
            "video_url": {
              "label": "URL",
              "info": "Bölümde başka bloklar varsa video açılır pencerede oynatılır.",
              "placeholder": "YouTube veya Vimeo URL'si kullanın"
            },
            "description": {
              "label": "Video alternatif metni",
              "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
              "default": "Videoyu açıklayın"
            }
          }
        }
      },
      "presets": {
        "name": "Kolaj"
      }
    },
    "collection-list": {
      "name": "Koleksiyon listesi",
      "settings": {
        "title": {
          "label": "Başlık",
          "default": "Koleksiyonlar"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "swipe_on_mobile": {
          "label": "Döngü"
        },
        "show_view_all": {
          "label": "\"Tümünü görüntüle\" düğmesi",
          "info": "Listede gösterilen daha fazla koleksiyon varsa görünür"
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "header_mobile": {
          "content": "Mobil düzen"
        },
        "columns_mobile": {
          "label": "Sütunlar",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_layout": {
          "content": "Düzen"
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Koleksiyon",
          "settings": {
            "collection": {
              "label": "Koleksiyon"
            }
          }
        }
      },
      "presets": {
        "name": "Koleksiyon listesi"
      }
    },
    "contact-form": {
      "name": "İletişim Formu",
      "presets": {
        "name": "İletişim formu"
      },
      "settings": {
        "title": {
          "default": "İletişim formu",
          "label": "Başlık"
        }
      }
    },
    "custom-liquid": {
      "name": "Özel Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Liquid kodu",
          "info": "Gelişmiş özelleştirmeler oluşturmak için uygulama parçacıkları veya başka bir kod ekleyin. [Daha fazla bilgi edinin](https://shopify.dev/docs/api/liquid)"
        }
      },
      "presets": {
        "name": "Özel Liquid"
      }
    },
    "featured-blog": {
      "name": "Blog gönderileri",
      "settings": {
        "heading": {
          "label": "Başlık",
          "default": "Blog gönderileri"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Gönderi sayısı"
        },
        "show_view_all": {
          "label": "\"Tümünü görüntüle\" düğmesi",
          "info": "Blogda gösterilenden daha fazla gönderi varsa görünür"
        },
        "show_image": {
          "label": "Öne çıkan görsel"
        },
        "show_date": {
          "label": "Tarih"
        },
        "show_author": {
          "label": "Yazar"
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "layout_header": {
          "content": "Düzen"
        },
        "text_header": {
          "content": "Metin"
        }
      },
      "presets": {
        "name": "Blog gönderileri"
      }
    },
    "featured-collection": {
      "name": "Öne çıkan koleksiyon",
      "settings": {
        "title": {
          "label": "Başlık",
          "default": "Öne çıkan koleksiyon"
        },
        "collection": {
          "label": "Koleksiyon"
        },
        "products_to_show": {
          "label": "Ürün sayısı"
        },
        "show_view_all": {
          "label": "\"Tümünü görüntüle\" düğmesi",
          "info": "Koleksiyonda gösterilenden daha fazla ürün varsa görünür"
        },
        "header": {
          "content": "Ürün kartı"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcı"
        },
        "show_rating": {
          "label": "Ürün derecelendirmesi",
          "info": "Derecelendirmeler için bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "description": {
          "label": "Açıklama"
        },
        "show_description": {
          "label": "Yönetici panelinden koleksiyon açıklamasını göster"
        },
        "description_style": {
          "label": "Açıklama stili",
          "options__1": {
            "label": "Gövde"
          },
          "options__2": {
            "label": "Alt yazı"
          },
          "options__3": {
            "label": "Büyük harf"
          }
        },
        "view_all_style": {
          "label": "\"Tümünü görüntüle\" stili",
          "options__1": {
            "label": "Bağlantı"
          },
          "options__2": {
            "label": "Dış çizgi düğmesi"
          },
          "options__3": {
            "label": "Sabit düğme"
          }
        },
        "enable_desktop_slider": {
          "label": "Döngü"
        },
        "full_width": {
          "label": "Tam genişlikli ürünler"
        },
        "header_mobile": {
          "content": "Mobil düzen"
        },
        "columns_mobile": {
          "label": "Sütunlar",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "swipe_on_mobile": {
          "label": "Döngü"
        },
        "enable_quick_buy": {
          "label": "Hızlı ekle"
        },
        "header_text": {
          "content": "Metin"
        },
        "header_collection": {
          "content": "Koleksiyon düzeni"
        }
      },
      "presets": {
        "name": "Öne çıkan koleksiyon"
      }
    },
    "footer": {
      "name": "Altbilgi",
      "blocks": {
        "link_list": {
          "name": "Menü",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Hızlı bağlantılar"
            },
            "menu": {
              "label": "Menü"
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Başlık"
            },
            "subtext": {
              "label": "Alt metin",
              "default": "<p>İletişim bilgilerini, mağaza ayrıntılarını ve marka içeriklerini müşterilerinizle paylaşın.</p>"
            }
          }
        },
        "brand_information": {
          "name": "Marka bilgileri",
          "settings": {
            "paragraph": {
              "content": "Marka ayarlarını [tema ayarları](/editor?context=theme&category=brand%20information) bölümünde yönetin"
            },
            "show_social": {
              "label": "Sosyal medya simgeleri",
              "info": "[Sosyal medya hesaplarını yönet](/editor?context=theme&category=social%20media)"
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "E-posta kaydı"
        },
        "newsletter_heading": {
          "label": "Başlık",
          "default": "E-posta listemize kaydolun"
        },
        "header__1": {
          "content": "E-posta kaydı",
          "info": "Kayıt ekleme [müşteri profilleri](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "show_social": {
          "label": "Sosyal medya simgeleri",
          "info": "[Sosyal medya hesaplarını yönet](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Ülke/bölge seçici",
          "info": "[Ülkeleri/bölgeleri yönet](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Dil seçici",
          "info": "[Dilleri yönet](/admin/settings/languages)"
        },
        "payment_enable": {
          "label": "Ödeme yöntemi simgeleri"
        },
        "margin_top": {
          "label": "Üst kenar boşluğu"
        },
        "show_policy": {
          "label": "Politika bağlantıları",
          "info": "[Politikaları yönet](/admin/settings/legal)"
        },
        "header__9": {
          "content": "Yardımcı araçlar"
        },
        "enable_follow_on_shop": {
          "label": "Shop'ta takip edin",
          "info": "Shop Pay etkinleştirilmelidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        }
      }
    },
    "header": {
      "name": "Üstbilgi",
      "settings": {
        "logo_position": {
          "label": "Logo konumu",
          "options__1": {
            "label": "Orta sol"
          },
          "options__2": {
            "label": "Üst sol"
          },
          "options__3": {
            "label": "Üst orta"
          },
          "options__4": {
            "label": "Orta kısmın ortası"
          }
        },
        "menu": {
          "label": "Menü"
        },
        "show_line_separator": {
          "label": "Ayırıcı satır"
        },
        "margin_bottom": {
          "label": "Alt kenar boşluğu"
        },
        "menu_type_desktop": {
          "label": "Menü türü",
          "options__1": {
            "label": "Açılır menü"
          },
          "options__2": {
            "label": "Mega menü"
          },
          "options__3": {
            "label": "Çekmece"
          }
        },
        "mobile_logo_position": {
          "label": "Mobil logo konumu",
          "options__1": {
            "label": "Orta"
          },
          "options__2": {
            "label": "Sol"
          }
        },
        "logo_help": {
          "content": "Logonuzu [tema ayarları](/editor?context=theme&category=logo) bölümünde düzenleyin"
        },
        "sticky_header_type": {
          "label": "Sabit üstbilgi",
          "options__1": {
            "label": "Yok"
          },
          "options__2": {
            "label": "Yukarı kaydırıldığında"
          },
          "options__3": {
            "label": "Her zaman"
          },
          "options__4": {
            "label": "Her zaman, logo boyutunu küçült"
          }
        },
        "enable_country_selector": {
          "label": "Ülke/bölge seçici",
          "info": "[Ülkeleri/bölgeleri yönet](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Dil seçici",
          "info": "[Dilleri yönet](/admin/settings/languages)"
        },
        "header__1": {
          "content": "Renk"
        },
        "menu_color_scheme": {
          "label": "Menü renk şeması"
        },
        "enable_customer_avatar": {
          "label": "Müşteri hesabı avatarı",
          "info": "Yalnızca müşteriler Shop ile oturum açtığında görünür [Müşteri hesaplarını yönet](/admin/settings/customer_accounts)"
        },
        "header__utilities": {
          "content": "Yardımcı araçlar"
        }
      }
    },
    "image-banner": {
      "name": "Görsel banner'ı",
      "settings": {
        "image": {
          "label": "Görsel 1"
        },
        "image_2": {
          "label": "Görsel 2"
        },
        "stack_images_on_mobile": {
          "label": "Görselleri üst üste ekle"
        },
        "show_text_box": {
          "label": "Kapsayıcı"
        },
        "image_overlay_opacity": {
          "label": "Yer paylaşımı opaklığı"
        },
        "show_text_below": {
          "label": "Kapsayıcı"
        },
        "image_height": {
          "label": "Yükseklik",
          "options__1": {
            "label": "İlk görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst Sol"
          },
          "options__2": {
            "label": "Üst Orta"
          },
          "options__3": {
            "label": "Üst Sağ"
          },
          "options__4": {
            "label": "Orta Sol"
          },
          "options__5": {
            "label": "Orta Kısmın Ortası"
          },
          "options__6": {
            "label": "Orta Sağ"
          },
          "options__7": {
            "label": "Alt Sol"
          },
          "options__8": {
            "label": "Alt Orta"
          },
          "options__9": {
            "label": "Alt Sağ"
          },
          "label": "Konum"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Hizalama"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Hizalama"
        },
        "mobile": {
          "content": "Mobil düzen"
        },
        "content": {
          "content": "İçerik"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Görsel banner'ı"
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "Müşterilerle şablonlardaki banner görseller veya içerikler hakkında ayrıntıları paylaşın."
            },
            "text_style": {
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              },
              "label": "Stil"
            }
          }
        },
        "buttons": {
          "name": "Düğmeler",
          "settings": {
            "button_label_1": {
              "label": "Etiket",
              "info": "Gizlemek için boş bırakın",
              "default": "Düğme etiketi"
            },
            "button_link_1": {
              "label": "Bağlantı"
            },
            "button_style_secondary_1": {
              "label": "Dış çizgi stili"
            },
            "button_label_2": {
              "label": "Etiket",
              "info": "Gizlemek için boş bırakın",
              "default": "Düğme etiketi"
            },
            "button_link_2": {
              "label": "Bağlantı"
            },
            "button_style_secondary_2": {
              "label": "Dış çizgi stili"
            },
            "header_1": {
              "content": "Düğme 1"
            },
            "header_2": {
              "content": "Düğme 2"
            }
          }
        }
      },
      "presets": {
        "name": "Görsel banner'ı"
      }
    },
    "image-with-text": {
      "name": "Metin içeren görsel",
      "settings": {
        "image": {
          "label": "Görsel"
        },
        "height": {
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "label": "Yükseklik",
          "options__4": {
            "label": "Büyük"
          }
        },
        "layout": {
          "options__1": {
            "label": "Önce görsel"
          },
          "options__2": {
            "label": "Görsel 2"
          },
          "label": "Yerleşim"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Küçük"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Büyük"
          },
          "label": "Genişlik"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Hizalama"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Alt"
          },
          "label": "Konum"
        },
        "content_layout": {
          "options__1": {
            "label": "Çakışma yok"
          },
          "options__2": {
            "label": "Çakışma"
          },
          "label": "Düzen"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Mobil hizalama"
        },
        "header": {
          "content": "İçerik"
        },
        "header_colors": {
          "content": "Renkler"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Metin içeren görsel"
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "<p>Metni bir görselle eşleyerek seçtiğiniz ürüne, koleksiyona veya blog gönderisine dikkat çekin. Stok durumu, stil hakkındaki ayrıntıları ekleyin, hatta inceleme sağlayın.</p>"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              }
            }
          }
        },
        "button": {
          "name": "Düğme",
          "settings": {
            "button_label": {
              "label": "Etiket",
              "info": "Gizlemek için boş bırakın",
              "default": "Düğme etiketi"
            },
            "button_link": {
              "label": "Bağlantı"
            },
            "outline_button": {
              "label": "Dış çizgi stili"
            }
          }
        },
        "caption": {
          "name": "Alt yazı",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "Reklam sloganı ekleyin"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Alt yazı"
              },
              "options__2": {
                "label": "Büyük harf"
              }
            },
            "caption_size": {
              "label": "Beden",
              "options__1": {
                "label": "Küçük"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Büyük"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Metin içeren görsel"
      }
    },
    "main-article": {
      "name": "Blog gönderisi",
      "blocks": {
        "featured_image": {
          "name": "Öne çıkan görsel",
          "settings": {
            "image_height": {
              "label": "Görsel yüksekliği",
              "options__1": {
                "label": "Görsele uyarla"
              },
              "options__2": {
                "label": "Küçük"
              },
              "options__3": {
                "label": "Orta"
              },
              "options__4": {
                "label": "Büyük"
              }
            }
          }
        },
        "title": {
          "name": "Başlık",
          "settings": {
            "blog_show_date": {
              "label": "Tarih"
            },
            "blog_show_author": {
              "label": "Yazar"
            }
          }
        },
        "content": {
          "name": "İçerik"
        },
        "share": {
          "name": "Paylaş",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "Paylaş"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blog gönderileri",
      "settings": {
        "show_image": {
          "label": "Öne çıkan görsel"
        },
        "show_date": {
          "label": "Tarih"
        },
        "show_author": {
          "label": "Yazar"
        },
        "layout": {
          "label": "Düzen",
          "options__1": {
            "label": "Izgara"
          },
          "options__2": {
            "label": "Kolaj"
          }
        },
        "image_height": {
          "label": "Görsel yüksekliği",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          }
        }
      }
    },
    "main-cart-footer": {
      "name": "Alt toplam",
      "blocks": {
        "subtotal": {
          "name": "Alt toplam fiyatı"
        },
        "buttons": {
          "name": "Ödeme düğmesi"
        }
      }
    },
    "main-cart-items": {
      "name": "Ürünler"
    },
    "main-collection-banner": {
      "name": "Koleksiyon banner'ı",
      "settings": {
        "paragraph": {
          "content": "Koleksiyon bilgileri [yönetici panelinizde yönetilir](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Açıklama"
        },
        "show_collection_image": {
          "label": "Görsel"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Ürün ızgarası",
      "settings": {
        "products_per_page": {
          "label": "Sayfa başına ürün"
        },
        "enable_filtering": {
          "label": "Filtreler",
          "info": "Filtreleri [Search & Discovery uygulaması](https://help.shopify.com/manual/online-store/search-and-discovery/filters) ile özelleştirin"
        },
        "enable_sorting": {
          "label": "Sıralama"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcı"
        },
        "header__1": {
          "content": "Filtreleme ve sıralama"
        },
        "header__3": {
          "content": "Ürün kartı"
        },
        "enable_tags": {
          "label": "Filtreler",
          "info": "Filtreleri [Search & Discovery uygulaması](https://help.shopify.com/manual/online-store/search-and-discovery/filters) ile özelleştirin"
        },
        "show_rating": {
          "label": "Ürün derecelendirmesi",
          "info": "Ürün derecelendirmeleri için bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "columns_mobile": {
          "label": "Mobil sütunlar",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "filter_type": {
          "label": "Filtre düzeni",
          "options__1": {
            "label": "Yatay"
          },
          "options__2": {
            "label": "Dikey"
          },
          "options__3": {
            "label": "Çekmece"
          }
        },
        "quick_add": {
          "label": "Hızlı ekleme",
          "options": {
            "option_1": "Yok",
            "option_2": "Standart",
            "option_3": "Toplu"
          }
        }
      }
    },
    "main-list-collections": {
      "name": "Koleksiyonlar listesi sayfası",
      "settings": {
        "title": {
          "label": "Başlık",
          "default": "Koleksiyonlar"
        },
        "sort": {
          "label": "Koleksiyonları sırala",
          "options__1": {
            "label": "Alfabetik olarak, A-Z"
          },
          "options__2": {
            "label": "Alfabetik olarak, Z-A"
          },
          "options__3": {
            "label": "Tarih, yeniden eskiye"
          },
          "options__4": {
            "label": "Tarih, eskiden yeniye"
          },
          "options__5": {
            "label": "Ürün sayısı, yüksekten düşüğe"
          },
          "options__6": {
            "label": "Ürün sayısı, düşükten yükseğe"
          }
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "header_mobile": {
          "content": "Mobil düzen"
        },
        "columns_mobile": {
          "label": "Mobil sütunlar",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "main-page": {
      "name": "Sayfa"
    },
    "main-password-footer": {
      "name": "Parola altbilgisi"
    },
    "main-password-header": {
      "name": "Parola üstbilgisi",
      "settings": {
        "logo_help": {
          "content": "Logonuzu [tema ayarları](/editor?context=theme&category=logo) bölümünde düzenleyin"
        }
      }
    },
    "main-product": {
      "blocks": {
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "Metin bloku"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        },
        "title": {
          "name": "Başlık"
        },
        "price": {
          "name": "Fiyat"
        },
        "quantity_selector": {
          "name": "Adet seçici"
        },
        "variant_picker": {
          "name": "Varyasyon seçici",
          "settings": {
            "picker_type": {
              "label": "Stil",
              "options__1": {
                "label": "Açılır liste"
              },
              "options__2": {
                "label": "Seçenekler"
              }
            },
            "swatch_shape": {
              "label": "Numune parça",
              "info": "Ürün seçeneklerinde [numune parçalar](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) hakkında daha fazla bilgi edinin.",
              "options__1": {
                "label": "Yuvarlak"
              },
              "options__2": {
                "label": "Kare"
              },
              "options__3": {
                "label": "Yok"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Satın al düğmeleri",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dinamik ödeme düğmeleri",
              "info": "Müşteriler tercih ettikleri ödeme seçeneğini görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            },
            "show_gift_card_recipient": {
              "label": " Hediye kartı gönderme seçenekleri",
              "info": "Müşteriler kişisel mesaj ekleyebilir ve gönderilme tarihini planlayabilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"
            }
          }
        },
        "pickup_availability": {
          "name": "Teslim alım stok durumu"
        },
        "description": {
          "name": "Açıklama"
        },
        "share": {
          "name": "Paylaş",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "Paylaş"
            }
          }
        },
        "collapsible_tab": {
          "name": "Daraltılabilir satır",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Daraltılabilir satır"
            },
            "content": {
              "label": "Satır içeriği"
            },
            "page": {
              "label": "Sayfadan alınan satır içeriği"
            },
            "icon": {
              "options__1": {
                "label": "Hiçbiri"
              },
              "options__2": {
                "label": "Elma"
              },
              "options__3": {
                "label": "Muz"
              },
              "options__4": {
                "label": "Şişe"
              },
              "options__5": {
                "label": "Kutu"
              },
              "options__6": {
                "label": "Havuç"
              },
              "options__7": {
                "label": "Sohbet balonu"
              },
              "options__8": {
                "label": "Onay işareti"
              },
              "options__9": {
                "label": "Pano"
              },
              "options__10": {
                "label": "Süt ürünü"
              },
              "options__11": {
                "label": "Süt ürünü içermez"
              },
              "options__12": {
                "label": "Kurutucu"
              },
              "options__13": {
                "label": "Göz"
              },
              "options__14": {
                "label": "Ateş"
              },
              "options__15": {
                "label": "Glütensiz"
              },
              "options__16": {
                "label": "Kalp"
              },
              "options__17": {
                "label": "Ütü"
              },
              "options__18": {
                "label": "Yaprak"
              },
              "options__19": {
                "label": "Deri"
              },
              "options__20": {
                "label": "Şimşek"
              },
              "options__21": {
                "label": "Ruj"
              },
              "options__22": {
                "label": "Kilit"
              },
              "options__23": {
                "label": "Harita pini"
              },
              "options__24": {
                "label": "Kabuklu yemişsiz"
              },
              "label": "Simge",
              "options__25": {
                "label": "Pantolon"
              },
              "options__26": {
                "label": "Pati izi"
              },
              "options__27": {
                "label": "Biber"
              },
              "options__28": {
                "label": "Parfüm"
              },
              "options__29": {
                "label": "Uçak"
              },
              "options__30": {
                "label": "Bitki"
              },
              "options__31": {
                "label": "Fiyat etiketi"
              },
              "options__32": {
                "label": "Soru işareti"
              },
              "options__33": {
                "label": "Geri dönüşüm"
              },
              "options__34": {
                "label": "İade"
              },
              "options__35": {
                "label": "Cetvel"
              },
              "options__36": {
                "label": "Servis tabağı"
              },
              "options__37": {
                "label": "Gömlek"
              },
              "options__38": {
                "label": "Ayakkabı"
              },
              "options__39": {
                "label": "Silüet"
              },
              "options__40": {
                "label": "Kar tanesi"
              },
              "options__41": {
                "label": "Yıldız"
              },
              "options__42": {
                "label": "Kronometre"
              },
              "options__43": {
                "label": "Kamyon"
              },
              "options__44": {
                "label": "Yıkama"
              }
            }
          }
        },
        "popup": {
          "name": "Açılır pencere",
          "settings": {
            "link_label": {
              "label": "Bağlantı etiketi",
              "default": "Açılır bağlantı metni"
            },
            "page": {
              "label": "Sayfa"
            }
          }
        },
        "rating": {
          "name": "Ürün puanı",
          "settings": {
            "paragraph": {
              "content": "Ürün derecelendirmeleri için bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Tamamlayıcı ürünler",
          "settings": {
            "paragraph": {
              "content": "Tamamlayıcı ürünleri [Search & Discovery uygulamasında](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations) yönetin"
            },
            "heading": {
              "label": "Başlık",
              "default": "Uygun eşleşmeler"
            },
            "make_collapsible_row": {
              "label": "Daraltılabilir satır"
            },
            "icon": {
              "info": "Daraltılabilir satır seçildiğinde gösterilir"
            },
            "product_list_limit": {
              "label": "Ürün sayısı"
            },
            "products_per_page": {
              "label": "Sayfa başına ürün sayısı"
            },
            "pagination_style": {
              "label": "Sayfalara ayırma",
              "options": {
                "option_1": "Noktalar",
                "option_2": "Sayaç",
                "option_3": "Numaralar"
              }
            },
            "product_card": {
              "heading": "Ürün kartı"
            },
            "image_ratio": {
              "label": "Görsel oranı",
              "options": {
                "option_1": "Portre",
                "option_2": "Kare"
              }
            },
            "enable_quick_add": {
              "label": "Hızlı ekle"
            }
          }
        },
        "icon_with_text": {
          "name": "Metin içeren simge",
          "settings": {
            "layout": {
              "label": "Düzen",
              "options__1": {
                "label": "Yatay"
              },
              "options__2": {
                "label": "Dikey"
              }
            },
            "heading": {
              "info": "Bu eşlemeyi gizlemek için boş bırakın"
            },
            "icon_1": {
              "label": "Simge"
            },
            "image_1": {
              "label": "Görsel"
            },
            "heading_1": {
              "label": "Başlık",
              "default": "Başlık"
            },
            "icon_2": {
              "label": "Simge"
            },
            "image_2": {
              "label": "Görsel"
            },
            "heading_2": {
              "label": "Başlık",
              "default": "Başlık"
            },
            "icon_3": {
              "label": "Simge"
            },
            "image_3": {
              "label": "Görsel"
            },
            "heading_3": {
              "label": "Başlık",
              "default": "Başlık"
            },
            "pairing_1": {
              "label": "Eşleme 1",
              "info": "Her eşleme için bir simge seçin veya bir görsel ekleyin"
            },
            "pairing_2": {
              "label": "Eşleme 2"
            },
            "pairing_3": {
              "label": "Eşleme 3"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        },
        "inventory": {
          "name": "Envanter durumu",
          "settings": {
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            },
            "inventory_threshold": {
              "label": "Düşük envanter eşiği"
            },
            "show_inventory_quantity": {
              "label": "Envanter sayımı"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Medya"
        },
        "enable_video_looping": {
          "label": "Video döngüsü"
        },
        "enable_sticky_info": {
          "label": "Sabit içerik"
        },
        "hide_variants": {
          "label": "Varyasyon medyası seçildiğinde diğer varyasyon medyalarını gizle"
        },
        "gallery_layout": {
          "label": "Düzen",
          "options__1": {
            "label": "Üst üste"
          },
          "options__2": {
            "label": "2 sütun"
          },
          "options__3": {
            "label": "Küçük resimler"
          },
          "options__4": {
            "label": "Küçük resim döngüsü"
          }
        },
        "media_size": {
          "label": "Genişlik",
          "options__1": {
            "label": "Küçük"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Büyük"
          }
        },
        "mobile_thumbnails": {
          "label": "Mobil düzen",
          "options__1": {
            "label": "2 sütun"
          },
          "options__2": {
            "label": "Küçük resimleri göster"
          },
          "options__3": {
            "label": "Küçük resimleri gizle"
          }
        },
        "media_position": {
          "label": "Konum",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Sağ"
          }
        },
        "image_zoom": {
          "label": "Yakınlaştırma",
          "options__1": {
            "label": "Lightbox'ı aç"
          },
          "options__2": {
            "label": "Tıkla ve imleci üzerine getir"
          },
          "options__3": {
            "label": "Yakınlaştırma yok"
          }
        },
        "constrain_to_viewport": {
          "label": "Ekran yüksekliğiyle sınırla"
        },
        "media_fit": {
          "label": "Sığdır",
          "options__1": {
            "label": "Orijinal"
          },
          "options__2": {
            "label": "Doldur"
          }
        }
      },
      "name": "Ürün bilgileri"
    },
    "main-search": {
      "name": "Arama sonuçları",
      "settings": {
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcı"
        },
        "header__1": {
          "content": "Ürün kartı"
        },
        "header__2": {
          "content": "Blog kartı"
        },
        "article_show_date": {
          "label": "Tarih"
        },
        "article_show_author": {
          "label": "Yazar"
        },
        "show_rating": {
          "label": "Ürün derecelendirmesi",
          "info": "Ürün derecelendirmeleri için bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "columns_mobile": {
          "label": "Mobil sütunlar",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Çoklu sütun",
      "settings": {
        "title": {
          "label": "Başlık",
          "default": "Çoklu sütun"
        },
        "image_width": {
          "label": "Genişlik",
          "options__1": {
            "label": "Sütun genişliğinin üçte biri"
          },
          "options__2": {
            "label": "Sütun genişliğinin yarısı"
          },
          "options__3": {
            "label": "Sütun genişliğinin tamamı"
          }
        },
        "image_ratio": {
          "label": "Oran",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          },
          "options__4": {
            "label": "Yuvarlak"
          }
        },
        "column_alignment": {
          "label": "Sütun hizalaması",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          }
        },
        "background_style": {
          "label": "İkincil arka plan",
          "options__1": {
            "label": "Hiçbiri"
          },
          "options__2": {
            "label": "Sütun arka planı olarak göster"
          }
        },
        "button_label": {
          "label": "Etiket",
          "default": "Düğme etiketi",
          "info": "Gizlemek için boş bırakın"
        },
        "button_link": {
          "label": "Bağlantı"
        },
        "swipe_on_mobile": {
          "label": "Döngü"
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "header_mobile": {
          "content": "Mobil düzen"
        },
        "columns_mobile": {
          "label": "Sütunlar",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_text": {
          "content": "Başlık"
        },
        "header_image": {
          "content": "Görsel"
        },
        "header_layout": {
          "content": "Düzen"
        },
        "header_button": {
          "content": "Düğme"
        }
      },
      "blocks": {
        "column": {
          "name": "Sütun",
          "settings": {
            "image": {
              "label": "Görsel"
            },
            "title": {
              "label": "Başlık",
              "default": "Sütun"
            },
            "text": {
              "label": "Açıklama",
              "default": "<p>Metni bir görselle eşleyerek seçtiğiniz ürüne, koleksiyona veya blog gönderisine dikkat çekin. Stok durumu, stil hakkındaki ayrıntıları ekleyin, hatta inceleme sağlayın.</p>"
            },
            "link_label": {
              "label": "Bağlantı etiketi",
              "info": "Gizlemek için boş bırakın"
            },
            "link": {
              "label": "Bağlantı"
            }
          }
        }
      },
      "presets": {
        "name": "Çoklu sütun"
      }
    },
    "newsletter": {
      "name": "E-posta kaydı",
      "settings": {
        "full_width": {
          "label": "Tam genişlik"
        },
        "paragraph": {
          "content": "Kayıt ekleme [müşteri profilleri](https://help.shopify.com/manual/customers/manage-customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "E-posta listemize kaydolun"
            }
          }
        },
        "paragraph": {
          "name": "Metin",
          "settings": {
            "paragraph": {
              "label": "Metin",
              "default": "<p>Yeni koleksiyonlar ve özel tekliflerden ilk siz haberdar olun.</p>"
            }
          }
        },
        "email_form": {
          "name": "E-posta formu"
        }
      },
      "presets": {
        "name": "E-posta kaydı"
      }
    },
    "page": {
      "name": "Sayfa",
      "settings": {
        "page": {
          "label": "Sayfa"
        }
      },
      "presets": {
        "name": "Sayfa"
      }
    },
    "rich-text": {
      "name": "Zengin metin",
      "settings": {
        "full_width": {
          "label": "Tam genişlik"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "İçerik konumu"
        },
        "content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "İçerik hizalaması"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Markanızdan bahsedin"
            }
          }
        },
        "text": {
          "name": "Metin rengi",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "<p>Müşterilerinizle markanız hakkında bilgi paylaşın. Ürün açıklaması girin, duyuru paylaşın veya mağazanıza gelen müşterileri karşılayın.</p>"
            }
          }
        },
        "buttons": {
          "name": "Düğmeler",
          "settings": {
            "button_label_1": {
              "label": "Etiket",
              "info": "Gizlemek için boş bırakın",
              "default": "Düğme etiketi"
            },
            "button_link_1": {
              "label": "Bağlantı"
            },
            "button_style_secondary_1": {
              "label": "Dış çizgi stili"
            },
            "button_label_2": {
              "label": "Etiket",
              "info": "Gizlemek için etiketi boş bırakın"
            },
            "button_link_2": {
              "label": "Bağlantı"
            },
            "button_style_secondary_2": {
              "label": "Dış çizgi stili"
            },
            "header_button1": {
              "content": "Düğme 1"
            },
            "header_button2": {
              "content": "Düğme 2"
            }
          }
        },
        "caption": {
          "name": "Alt yazı",
          "settings": {
            "text": {
              "label": "Metin rengi",
              "default": "Reklam sloganı ekleyin"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Alt yazı"
              },
              "options__2": {
                "label": "Büyük harf"
              }
            },
            "caption_size": {
              "label": "Beden",
              "options__1": {
                "label": "Küçük"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Büyük"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Zengin metin"
      }
    },
    "apps": {
      "name": "Uygulamalar",
      "settings": {
        "include_margins": {
          "label": "Bölüm kenar boşluklarını temayla aynı yap"
        }
      },
      "presets": {
        "name": "Uygulamalar"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Başlık",
          "default": "Video"
        },
        "cover_image": {
          "label": "Kapak görseli"
        },
        "video_url": {
          "label": "URL",
          "info": "YouTube veya Vimeo URL'si kullan"
        },
        "description": {
          "label": "Video alternatif metni",
          "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın"
        },
        "image_padding": {
          "label": "Görsel dolgusu ekle",
          "info": "Kapak görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."
        },
        "full_width": {
          "label": "Tam genişlik"
        },
        "video": {
          "label": "Video"
        },
        "enable_video_looping": {
          "label": "Video döngüsü"
        },
        "header__1": {
          "content": "Shopify'da barındırılan videolar"
        },
        "header__2": {
          "content": "URL'den video ekle"
        },
        "header__3": {
          "content": "Düzen"
        },
        "paragraph": {
          "content": "Shopify'da barındırılan bir video seçilmediğinde gösterilir"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Öne çıkan ürün",
      "blocks": {
        "text": {
          "name": "Metin",
          "settings": {
            "text": {
              "label": "Metin",
              "default": "Metin bloku"
            },
            "text_style": {
              "label": "Stil",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        },
        "title": {
          "name": "Başlık"
        },
        "price": {
          "name": "Fiyat"
        },
        "quantity_selector": {
          "name": "Adet seçici"
        },
        "variant_picker": {
          "name": "Varyasyon seçici",
          "settings": {
            "picker_type": {
              "label": "Stil",
              "options__1": {
                "label": "Açılır menü"
              },
              "options__2": {
                "label": "Seçenekler"
              }
            },
            "swatch_shape": {
              "label": "Numune parça",
              "info": "Ürün seçeneklerinde [numune parçalar](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) hakkında daha fazla bilgi edinin.",
              "options__1": {
                "label": "Yuvarlak"
              },
              "options__2": {
                "label": "Kare"
              },
              "options__3": {
                "label": "Yok"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Satın al düğmeleri",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dinamik ödeme düğmeleri",
              "info": "Müşteriler tercih ettikleri ödeme seçeneğini görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Açıklama"
        },
        "share": {
          "name": "Paylaş",
          "settings": {
            "featured_image_info": {
              "content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, önizleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Mağaza başlığı ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Metin rengi",
              "default": "Paylaş"
            }
          }
        },
        "rating": {
          "name": "Ürün puanı",
          "settings": {
            "paragraph": {
              "content": "Ürün derecelendirmeleri için bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU",
          "settings": {
            "text_style": {
              "label": "Metin stili",
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "options__3": {
                "label": "Büyük harf"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Ürün"
        },
        "secondary_background": {
          "label": "İkincil arka plan"
        },
        "header": {
          "content": "Medya"
        },
        "enable_video_looping": {
          "label": "Video döngüsü"
        },
        "hide_variants": {
          "label": "Masaüstünde seçimi kaldırılmış varyasyonların medyasını gizle"
        },
        "media_position": {
          "label": "Konum",
          "info": "Konum, mobil cihazlar için otomatik olarak optimize edilir.",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Sağ"
          }
        }
      },
      "presets": {
        "name": "Öne çıkan ürün"
      }
    },
    "email-signup-banner": {
      "name": "E-posta kaydı banner'ı",
      "settings": {
        "paragraph": {
          "content": "Kayıt ekleme [müşteri profilleri](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "image": {
          "label": "Arka plan resmi"
        },
        "show_background_image": {
          "label": "Arka plan resmini göster"
        },
        "show_text_box": {
          "label": "Kapsayıcı"
        },
        "image_overlay_opacity": {
          "label": "Yer paylaşımı opaklığı"
        },
        "show_text_below": {
          "label": "Görselin altına metin ekle"
        },
        "image_height": {
          "label": "Yükseklik",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst Sol"
          },
          "options__2": {
            "label": "Üst Orta"
          },
          "options__3": {
            "label": "Üst Sağ"
          },
          "options__4": {
            "label": "Orta Sol"
          },
          "options__5": {
            "label": "Orta Kısmın Ortası"
          },
          "options__6": {
            "label": "Orta Sağ"
          },
          "options__7": {
            "label": "Alt Sol"
          },
          "options__8": {
            "label": "Alt Orta"
          },
          "options__9": {
            "label": "Alt Sağ"
          },
          "label": "Konum"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Hizalama"
        },
        "header": {
          "content": "Mobil düzen"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Hizalama"
        },
        "color_scheme": {
          "info": "Kapsayıcı gösterildiğinde görünür."
        },
        "content_header": {
          "content": "İçerik"
        }
      },
      "blocks": {
        "heading": {
          "name": "Başlık",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Yakında açılıyor"
            }
          }
        },
        "paragraph": {
          "name": "Metin",
          "settings": {
            "paragraph": {
              "label": "Metin",
              "default": "<p>Yeni çıkardıklarımızı ilk siz öğrenin.</p>"
            },
            "text_style": {
              "options__1": {
                "label": "Gövde"
              },
              "options__2": {
                "label": "Alt yazı"
              },
              "label": "Stil"
            }
          }
        },
        "email_form": {
          "name": "E-posta formu"
        }
      },
      "presets": {
        "name": "E-posta kaydı banner'ı"
      }
    },
    "slideshow": {
      "name": "Slayt gösterisi",
      "settings": {
        "layout": {
          "label": "Düzen",
          "options__1": {
            "label": "Tam genişlik"
          },
          "options__2": {
            "label": "Sayfa"
          }
        },
        "slide_height": {
          "label": "Yükseklik",
          "options__1": {
            "label": "İlk görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          }
        },
        "slider_visual": {
          "label": "Sayfalara ayırma",
          "options__1": {
            "label": "Sayaç"
          },
          "options__2": {
            "label": "Noktalar"
          },
          "options__3": {
            "label": "Numaralar"
          }
        },
        "auto_rotate": {
          "label": "Slaytları otomatik olarak döndür"
        },
        "change_slides_speed": {
          "label": "Slaytları şu zaman aralığında değiştir:"
        },
        "show_text_below": {
          "label": "Görselin altına metin ekle"
        },
        "mobile": {
          "content": "Mobil düzen"
        },
        "accessibility": {
          "content": "Erişilebilirlik",
          "label": "Slayt gösterisi açıklaması",
          "info": "Ekran okuyucu kullanan müşteriler için slayt gösterisini açıklayın",
          "default": "Markamız hakkında slayt gösterisi"
        }
      },
      "blocks": {
        "slide": {
          "name": "Slayt",
          "settings": {
            "image": {
              "label": "Görsel"
            },
            "heading": {
              "label": "Başlık",
              "default": "Görsel slaytı"
            },
            "subheading": {
              "label": "Alt başlık",
              "default": "Görsellerle marka öykünüzü anlatın"
            },
            "button_label": {
              "label": "Etiket",
              "info": "Gizlemek için boş bırakın",
              "default": "Düğme etiketi"
            },
            "link": {
              "label": "Bağlantı"
            },
            "secondary_style": {
              "label": "Dış çizgi stili"
            },
            "box_align": {
              "label": "İçerik konumu",
              "options__1": {
                "label": "Üst sol"
              },
              "options__2": {
                "label": "Üst orta"
              },
              "options__3": {
                "label": "Üst sağ"
              },
              "options__4": {
                "label": "Orta sol"
              },
              "options__5": {
                "label": "Orta kısmın ortası"
              },
              "options__6": {
                "label": "Orta sağ"
              },
              "options__7": {
                "label": "Alt sol"
              },
              "options__8": {
                "label": "Alt orta"
              },
              "options__9": {
                "label": "Alt sağ"
              }
            },
            "show_text_box": {
              "label": "Kapsayıcı"
            },
            "text_alignment": {
              "label": "İçerik hizalaması",
              "option_1": {
                "label": "Sol"
              },
              "option_2": {
                "label": "Orta"
              },
              "option_3": {
                "label": "Sağ"
              }
            },
            "image_overlay_opacity": {
              "label": "Yer paylaşımı opaklığı"
            },
            "text_alignment_mobile": {
              "label": "Mobil içerik hizalaması",
              "options__1": {
                "label": "Sol"
              },
              "options__2": {
                "label": "Orta"
              },
              "options__3": {
                "label": "Sağ"
              }
            },
            "header_button": {
              "content": "Düğme"
            },
            "header_layout": {
              "content": "Düzen"
            },
            "header_text": {
              "content": "Metin"
            },
            "header_colors": {
              "content": "Renkler"
            }
          }
        }
      },
      "presets": {
        "name": "Slayt gösterisi"
      }
    },
    "collapsible_content": {
      "name": "Daraltılabilir içerik",
      "settings": {
        "caption": {
          "label": "Alt yazı"
        },
        "heading": {
          "label": "Başlık",
          "default": "Daraltılabilir içerik"
        },
        "heading_alignment": {
          "label": "Başlık hizalaması",
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          }
        },
        "layout": {
          "label": "Kapsayıcı",
          "options__1": {
            "label": "Kapsayıcı yok"
          },
          "options__2": {
            "label": "Satır kapsayıcı"
          },
          "options__3": {
            "label": "Bölüm kapsayıcı"
          }
        },
        "container_color_scheme": {
          "label": "Kapsayıcı renk şeması"
        },
        "open_first_collapsible_row": {
          "label": "İlk satırı aç"
        },
        "header": {
          "content": "Görsel"
        },
        "image": {
          "label": "Görsel"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Büyük"
          }
        },
        "desktop_layout": {
          "label": "Yerleşim",
          "options__1": {
            "label": "Önce görsel"
          },
          "options__2": {
            "label": "İkinci olarak görsel"
          }
        },
        "layout_header": {
          "content": "Düzen"
        },
        "section_color_scheme": {
          "label": "Bölüm renk düzeni"
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Daraltılabilir satır",
          "settings": {
            "heading": {
              "label": "Başlık",
              "default": "Daraltılabilir satır"
            },
            "row_content": {
              "label": "Satır içeriği"
            },
            "page": {
              "label": "Sayfadan alınan satır içeriği"
            },
            "icon": {
              "label": "Simge",
              "options__1": {
                "label": "Hiçbiri"
              },
              "options__2": {
                "label": "Elma"
              },
              "options__3": {
                "label": "Muz"
              },
              "options__4": {
                "label": "Şişe"
              },
              "options__5": {
                "label": "Kutu"
              },
              "options__6": {
                "label": "Havuç"
              },
              "options__7": {
                "label": "Sohbet balonu"
              },
              "options__8": {
                "label": "Onay işareti"
              },
              "options__9": {
                "label": "Pano"
              },
              "options__10": {
                "label": "Süt ürünü"
              },
              "options__11": {
                "label": "Süt ürünü içermez"
              },
              "options__12": {
                "label": "Kurutucu"
              },
              "options__13": {
                "label": "Göz"
              },
              "options__14": {
                "label": "Ateş"
              },
              "options__15": {
                "label": "Glütensiz"
              },
              "options__16": {
                "label": "Kalp"
              },
              "options__17": {
                "label": "Ütü"
              },
              "options__18": {
                "label": "Yaprak"
              },
              "options__19": {
                "label": "Deri"
              },
              "options__20": {
                "label": "Şimşek"
              },
              "options__21": {
                "label": "Ruj"
              },
              "options__22": {
                "label": "Kilit"
              },
              "options__23": {
                "label": "Harita pini"
              },
              "options__24": {
                "label": "Kabuklu yemişsiz"
              },
              "options__25": {
                "label": "Pantolon"
              },
              "options__26": {
                "label": "Pati izi"
              },
              "options__27": {
                "label": "Biber"
              },
              "options__28": {
                "label": "Parfüm"
              },
              "options__29": {
                "label": "Uçak"
              },
              "options__30": {
                "label": "Bitki"
              },
              "options__31": {
                "label": "Fiyat etiketi"
              },
              "options__32": {
                "label": "Soru işareti"
              },
              "options__33": {
                "label": "Geri dönüşüm"
              },
              "options__34": {
                "label": "İade"
              },
              "options__35": {
                "label": "Cetvel"
              },
              "options__36": {
                "label": "Servis tabağı"
              },
              "options__37": {
                "label": "Gömlek"
              },
              "options__38": {
                "label": "Ayakkabı"
              },
              "options__39": {
                "label": "Silüet"
              },
              "options__40": {
                "label": "Kar tanesi"
              },
              "options__41": {
                "label": "Yıldız"
              },
              "options__42": {
                "label": "Kronometre"
              },
              "options__43": {
                "label": "Kamyon"
              },
              "options__44": {
                "label": "Yıkama"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Daraltılabilir içerik"
      }
    },
    "main-account": {
      "name": "Hesap"
    },
    "main-activate-account": {
      "name": "Hesap etkinleştirme"
    },
    "main-addresses": {
      "name": "Adresler"
    },
    "main-login": {
      "name": "Giriş bilgileri",
      "shop_login_button": {
        "enable": "Shop ile giriş yapmayı etkinleştirin"
      }
    },
    "main-order": {
      "name": "Sipariş"
    },
    "main-register": {
      "name": "Kayıt"
    },
    "main-reset-password": {
      "name": "Parola sıfırlama"
    },
    "related-products": {
      "name": "Alakalı ürünler",
      "settings": {
        "heading": {
          "label": "Başlık"
        },
        "products_to_show": {
          "label": "Ürün sayısı"
        },
        "columns_desktop": {
          "label": "Sütunlar"
        },
        "paragraph__1": {
          "content": "İlgili ürünler [Search & Discovery uygulamasında](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations) yönetilebilir",
          "default": "Şu ürünler de hoşunuza gidebilir:"
        },
        "header__2": {
          "content": "Ürün kartı"
        },
        "image_ratio": {
          "label": "Görsel oranı",
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Portre"
          },
          "options__3": {
            "label": "Kare"
          }
        },
        "show_secondary_image": {
          "label": "Üstüne gelindiğinde ikinci görseli göster"
        },
        "show_vendor": {
          "label": "Satıcı"
        },
        "show_rating": {
          "label": "Ürün derecelendirmesi",
          "info": "Ürün derecelendirmeleri için bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"
        },
        "columns_mobile": {
          "label": "Mobil sütunlar",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multirow": {
      "name": "Çok satırlı",
      "settings": {
        "image": {
          "label": "Görsel"
        },
        "image_height": {
          "options__1": {
            "label": "Görsele uyarla"
          },
          "options__2": {
            "label": "Küçük"
          },
          "options__3": {
            "label": "Orta"
          },
          "options__4": {
            "label": "Büyük"
          },
          "label": "Yükseklik"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Küçük"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Büyük"
          },
          "label": "Genişlik"
        },
        "text_style": {
          "options__1": {
            "label": "Gövde"
          },
          "options__2": {
            "label": "Alt yazı"
          },
          "label": "Metin stili"
        },
        "button_style": {
          "options__1": {
            "label": "Sabit düğme"
          },
          "options__2": {
            "label": "Dış çizgi düğmesi"
          },
          "label": "Düğme stili"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Hizalama"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Üst"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Alt"
          },
          "label": "Konum"
        },
        "image_layout": {
          "options__1": {
            "label": "Alternatif (soldan)"
          },
          "options__2": {
            "label": "Alternatif (sağdan)"
          },
          "options__3": {
            "label": "Sola hizalanmış"
          },
          "options__4": {
            "label": "Sağa hizalanmış"
          },
          "label": "Yerleşim"
        },
        "container_color_scheme": {
          "label": "Kapsayıcı renk şeması"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Sol"
          },
          "options__2": {
            "label": "Orta"
          },
          "options__3": {
            "label": "Sağ"
          },
          "label": "Mobil hizalama"
        },
        "header": {
          "content": "Görsel"
        },
        "header_2": {
          "content": "İçerik"
        },
        "header_3": {
          "content": "Renkler"
        }
      },
      "blocks": {
        "row": {
          "name": "Satır",
          "settings": {
            "image": {
              "label": "Görsel"
            },
            "caption": {
              "label": "Alt yazı",
              "default": "Alt yazı"
            },
            "heading": {
              "label": "Başlık",
              "default": "Satır"
            },
            "text": {
              "label": "Metin",
              "default": "<p>Metni bir görselle eşleyerek seçtiğiniz ürüne, koleksiyona veya blog gönderisine dikkat çekin. Stok durumu, stil hakkındaki ayrıntıları ekleyin, hatta inceleme sağlayın.</p>"
            },
            "button_label": {
              "label": "Düğme etiketi",
              "default": "Düğme etiketi",
              "info": "Gizlemek için boş bırakın"
            },
            "button_link": {
              "label": "Düğme bağlantısı"
            }
          }
        }
      },
      "presets": {
        "name": "Çok satırlı"
      }
    },
    "quick-order-list": {
      "name": "Hızlı sipariş listesi",
      "settings": {
        "show_image": {
          "label": "Görseller"
        },
        "show_sku": {
          "label": "SKU'lar"
        },
        "variants_per_page": {
          "label": "Sayfa başına varyasyon"
        }
      },
      "presets": {
        "name": "Hızlı sipariş listesi"
      }
    }
  }
}
