/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "settings_schema": {
    "colors": {
      "name": "Värit",
      "settings": {
        "background": {
          "label": "Tausta"
        },
        "background_gradient": {
          "label": "Taustan liukuväri",
          "info": "Taustan liukuväri korvaa taustan silloin kun se on mahdollista."
        },
        "text": {
          "label": "Teksti"
        },
        "button_background": {
          "label": "Peittävän painik<PERSON> tausta"
        },
        "button_label": {
          "label": "Peittävä tekstipainike"
        },
        "secondary_button_label": {
          "label": "Kehy<PERSON>ainike"
        },
        "shadow": {
          "label": "Varjo"
        }
      }
    },
    "typography": {
      "name": "Typografia",
      "settings": {
        "type_header_font": {
          "label": "Fontti"
        },
        "header__1": {
          "content": "Otsikot"
        },
        "header__2": {
          "content": "Leipäteksti"
        },
        "type_body_font": {
          "label": "Fontti"
        },
        "heading_scale": {
          "label": "Skaalaa"
        },
        "body_scale": {
          "label": "Skaalaa"
        }
      }
    },
    "social-media": {
      "name": "Sosiaalinen media",
      "settings": {
        "social_twitter_link": {
          "label": "X (Twitter)",
          "info": "https://x.com/shopify"
        },
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://facebook.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://facebook.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "https://instagram.com/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://twitter.com/shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "http://shopify.tumblr.com"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://facebook.com/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header": {
          "content": "Sosiaalisen median tilit"
        }
      }
    },
    "currency_format": {
      "name": "Valuutan muoto",
      "settings": {
        "currency_code_enabled": {
          "label": "Valuuttakoodit"
        },
        "paragraph": "Ostoskorin ja kassan hinnat näyttävät aina valuuttakoodit"
      }
    },
    "layout": {
      "name": "Asettelu",
      "settings": {
        "page_width": {
          "label": "Sivun leveys"
        },
        "spacing_sections": {
          "label": "Tila malliosioiden välissä"
        },
        "header__grid": {
          "content": "Ruudukko"
        },
        "paragraph__grid": {
          "content": "Vaikuttaa alueisiin, joissa on monta saraketta tai riviä"
        },
        "spacing_grid_horizontal": {
          "label": "Vaakasuuntainen tila"
        },
        "spacing_grid_vertical": {
          "label": "Pystysuuntainen tila"
        }
      }
    },
    "search_input": {
      "name": "Hakukäyttäytyminen",
      "settings": {
        "predictive_search_enabled": {
          "label": "Hakuehdotukset"
        },
        "predictive_search_show_vendor": {
          "label": "Tuotteen myyjä",
          "info": "Näytetään, kun hakuehdotukset ovat käytössä"
        },
        "predictive_search_show_price": {
          "label": "Tuotteen hinta",
          "info": "Näytetään, kun hakuehdotukset ovat käytössä"
        }
      }
    },
    "global": {
      "settings": {
        "header__border": {
          "content": "Reuna"
        },
        "header__shadow": {
          "content": "Varjo"
        },
        "blur": {
          "label": "Sumeus"
        },
        "corner_radius": {
          "label": "Kulman säde"
        },
        "horizontal_offset": {
          "label": "Vaakasuuntainen siirtymä"
        },
        "vertical_offset": {
          "label": "Pystysuuntainen siirtymä"
        },
        "thickness": {
          "label": "Paksuus"
        },
        "opacity": {
          "label": "Sameus"
        },
        "image_padding": {
          "label": "Kuvan täyttäminen"
        },
        "text_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Tekstin tasaus"
        }
      }
    },
    "cards": {
      "name": "Tuotekortit",
      "settings": {
        "style": {
          "options__1": {
            "label": "Vakiomuotoinen"
          },
          "options__2": {
            "label": "Kortti"
          },
          "label": "Tyyli"
        }
      }
    },
    "badges": {
      "name": "Tunnukset",
      "settings": {
        "position": {
          "options__1": {
            "label": "Alhaalla vasemmalla"
          },
          "options__2": {
            "label": "Alhaalla oikealla"
          },
          "options__3": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__4": {
            "label": "Ylhäällä oikealla"
          },
          "label": "Sijainti korteilla"
        },
        "sale_badge_color_scheme": {
          "label": "Alennusmyynti-tunnuksen värimalli"
        },
        "sold_out_badge_color_scheme": {
          "label": "Loppuunmyyty-tunnuksen värimalli"
        }
      }
    },
    "buttons": {
      "name": "Painikkeet"
    },
    "variant_pills": {
      "name": "Versioiden kuvakkeet",
      "paragraph": "Versiokuvakkeet ovat yksi tapa näyttää [tuoteversiot](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block)"
    },
    "inputs": {
      "name": "Syötteet"
    },
    "content_containers": {
      "name": "Sisältösäiliöt"
    },
    "popups": {
      "name": "Alasvetovalikot ja ponnahdusikkunat",
      "paragraph": "Vaikuttaa navigoinnin alasvetovalikoiden, modaalisiin ponnahdusikkunoiden ja ostoskorin kaltaisiin alueisiin"
    },
    "media": {
      "name": "Tietovälineet"
    },
    "drawers": {
      "name": "Vetolaatikot"
    },
    "cart": {
      "name": "Ostoskori",
      "settings": {
        "cart_type": {
          "label": "Tyyppi",
          "drawer": {
            "label": "Laatikko"
          },
          "page": {
            "label": "Sivu"
          },
          "notification": {
            "label": "Ponnahdusikkunailmoitukset"
          }
        },
        "show_vendor": {
          "label": "Myyjä"
        },
        "show_cart_note": {
          "label": "Tilauskommentti"
        },
        "cart_drawer": {
          "header": "Veto-ostoskori",
          "collection": {
            "label": "Kokoelma",
            "info": "Näytetään, kun veto-ostoskori on tyhjä"
          }
        }
      }
    },
    "collection_cards": {
      "name": "Kokoelman kortit",
      "settings": {
        "style": {
          "options__1": {
            "label": "Vakio"
          },
          "options__2": {
            "label": "Kortti"
          },
          "label": "Tyyli"
        }
      }
    },
    "blog_cards": {
      "name": "Blogi-kortit",
      "settings": {
        "style": {
          "options__1": {
            "label": "Vakio"
          },
          "options__2": {
            "label": "Kortti"
          },
          "label": "Tyyli"
        }
      }
    },
    "logo": {
      "name": "Logo",
      "settings": {
        "logo_image": {
          "label": "Logo"
        },
        "logo_width": {
          "label": "Leveys"
        },
        "favicon": {
          "label": "Favicon",
          "info": "Näytetään koossa 32 x 32px"
        }
      }
    },
    "brand_information": {
      "name": "Bränditiedot",
      "settings": {
        "brand_headline": {
          "label": "Otsikko"
        },
        "brand_description": {
          "label": "Oikeuksien kuvaus"
        },
        "brand_image": {
          "label": "Kuva"
        },
        "brand_image_width": {
          "label": "Kuvan leveys"
        },
        "paragraph": {
          "content": "Näytetään alatunnisteen bränditietolohkossa"
        }
      }
    },
    "animations": {
      "name": "Animaatiot",
      "settings": {
        "animations_reveal_on_scroll": {
          "label": "Näytä osiot vierittämällä"
        },
        "animations_hover_elements": {
          "options__1": {
            "label": "Ei mitään"
          },
          "options__2": {
            "label": "Pystysuora nosto"
          },
          "label": "Osoitusefekti",
          "info": "Vaikuttaa kortteihin ja painikkeisiin",
          "options__3": {
            "label": "3D-nosto"
          }
        }
      }
    }
  },
  "sections": {
    "all": {
      "padding": {
        "section_padding_heading": "Puskuri",
        "padding_top": "Yläreuna",
        "padding_bottom": "Alareuna"
      },
      "spacing": "Väli",
      "colors": {
        "label": "Värimalli",
        "has_cards_info": "Voit muuttaa kortin väriskeemaa päivittämällä teema-asetuksia."
      },
      "heading_size": {
        "label": "Otsikon koko",
        "options__1": {
          "label": "Pieni"
        },
        "options__2": {
          "label": "Keskisuuri"
        },
        "options__3": {
          "label": "Suuri"
        },
        "options__4": {
          "label": "Erittäin suuri"
        },
        "options__5": {
          "label": "XXL"
        }
      },
      "image_shape": {
        "options__1": {
          "label": "Oletus"
        },
        "options__2": {
          "label": "Kaari"
        },
        "options__3": {
          "label": "Möykky"
        },
        "options__4": {
          "label": "V-merkki vasemmalle"
        },
        "options__5": {
          "label": "V-merkki oikealle"
        },
        "options__6": {
          "label": "Timantti"
        },
        "options__7": {
          "label": "Suunnikas"
        },
        "options__8": {
          "label": "Pyörylä"
        },
        "label": "Kuvan muoto"
      },
      "animation": {
        "content": "Animaatiot",
        "image_behavior": {
          "options__1": {
            "label": "Ei mitään"
          },
          "options__2": {
            "label": "Ympäristön liike"
          },
          "label": "Animaatio",
          "options__3": {
            "label": "Kiinteä taustasijainti"
          },
          "options__4": {
            "label": "Lähennä vierittäessäsi"
          }
        }
      }
    },
    "announcement-bar": {
      "name": "Ilmoituspalkki",
      "blocks": {
        "announcement": {
          "name": "Ilmoitus",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Tervetuloa kauppaamme"
            },
            "text_alignment": {
              "label": "Tekstin tasaus",
              "options__1": {
                "label": "Vasen"
              },
              "options__2": {
                "label": "Keskitetty"
              },
              "options__3": {
                "label": "Oikea"
              }
            },
            "link": {
              "label": "Linkki"
            }
          }
        }
      },
      "settings": {
        "auto_rotate": {
          "label": "Käännä ilmoitukset automaattisesti"
        },
        "change_slides_speed": {
          "label": "Vaihda joka"
        },
        "show_social": {
          "label": "Somekuvakkeet",
          "info": "[Hallinnoi sometilejä](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Maa-/aluevalitsin",
          "info": "[Hallinnoi maita/alueita](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Kielivalitsin",
          "info": "[Hallinnoi kieliä](/admin/settings/languages)"
        },
        "heading_utilities": {
          "content": "Apuohjelmat"
        },
        "paragraph": {
          "content": "Näytetään vain suurilla näytöillä"
        }
      },
      "presets": {
        "name": "Ilmoituspalkki"
      }
    },
    "collage": {
      "name": "Kollaasi",
      "settings": {
        "heading": {
          "label": "Otsikko",
          "default": "Multimediakollaasi"
        },
        "desktop_layout": {
          "label": "Asettelu",
          "options__1": {
            "label": "Suuri lohko ensimmäisenä"
          },
          "options__2": {
            "label": "Suuri lohko viimeisenä"
          }
        },
        "mobile_layout": {
          "label": "Mobiiliasettelu",
          "options__1": {
            "label": "Kollaasi"
          },
          "options__2": {
            "label": "Sarake"
          }
        },
        "card_styles": {
          "label": "Kortin tyyli",
          "info": "Hallinnoi yksittäisiä korttityylejä [teema-asetuksissa](/editor?context=theme&category=product%20cards)",
          "options__1": {
            "label": "Käytä yksittäisiä korttityylejä"
          },
          "options__2": {
            "label": "Muuta kaikki tuotekorteiksi"
          }
        },
        "header_layout": {
          "content": "Asettelu"
        }
      },
      "blocks": {
        "image": {
          "name": "Kuva",
          "settings": {
            "image": {
              "label": "Kuva"
            }
          }
        },
        "product": {
          "name": "Tuote",
          "settings": {
            "product": {
              "label": "Tuote"
            },
            "secondary_background": {
              "label": "Näytä toissijainen tausta"
            },
            "second_image": {
              "label": "Näytä toinen kuva osoittaessa"
            }
          }
        },
        "collection": {
          "name": "Kokoelma",
          "settings": {
            "collection": {
              "label": "Kokoelma"
            }
          }
        },
        "video": {
          "name": "Video",
          "settings": {
            "cover_image": {
              "label": "Kansikuva"
            },
            "video_url": {
              "label": "URL-osoite",
              "info": "Video toistetaan ponnahdusikkunassa, jos osio sisältää muita lohkoja.",
              "placeholder": "Käytä YouTube- tai Vimeo-linkkiä"
            },
            "description": {
              "label": "Videon vaihtoehtoinen teksti",
              "info": "Kuvaile videota näytönlukijoita käyttäviä asiakkaita varten. [Lisätietoja](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)",
              "default": "Kuvaile videota"
            }
          }
        }
      },
      "presets": {
        "name": "Kollaasi"
      }
    },
    "collection-list": {
      "name": "Kokoelmaluettelo",
      "settings": {
        "title": {
          "label": "Otsikko",
          "default": "Kokoelmat"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "swipe_on_mobile": {
          "label": "Karuselli"
        },
        "show_view_all": {
          "label": "Näytä kaikki ‑painike",
          "info": "Näkyvissä, jos luettelossa on näytettyä enemmän kokoelmia"
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "header_mobile": {
          "content": "Mobiiliasettelu"
        },
        "columns_mobile": {
          "label": "Sarakkeet",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_layout": {
          "content": "Asettelu"
        }
      },
      "blocks": {
        "featured_collection": {
          "name": "Kokoelma",
          "settings": {
            "collection": {
              "label": "Kokoelma"
            }
          }
        }
      },
      "presets": {
        "name": "Kokoelmaluettelo"
      }
    },
    "contact-form": {
      "name": "Yhteydenottolomake",
      "presets": {
        "name": "Yhteydenottolomake"
      },
      "settings": {
        "title": {
          "default": "Yhteydenottolomake",
          "label": "Otsikko"
        }
      }
    },
    "custom-liquid": {
      "name": "Mukautettu Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Liquid-koodi",
          "info": "Luo vaativampia mukautuksia lisäämällä sovelluksen koodinpätkiä tai muita koodeja. [Lisätietoja](https://shopify.dev/docs/api/liquid)"
        }
      },
      "presets": {
        "name": "Mukautettu Liquid"
      }
    },
    "featured-blog": {
      "name": "Blogipostaukset",
      "settings": {
        "heading": {
          "label": "Otsikko",
          "default": "Blogipostaukset"
        },
        "blog": {
          "label": "Blogi"
        },
        "post_limit": {
          "label": "Postausten määrä"
        },
        "show_view_all": {
          "label": "Näytä kaikki ‑painike",
          "info": "Näkyvissä, jos blogissa on näytettyä enemmän postauksia"
        },
        "show_image": {
          "label": "Esittelykuva"
        },
        "show_date": {
          "label": "Päivämäärä"
        },
        "show_author": {
          "label": "Tekijä"
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "layout_header": {
          "content": "Asettelu"
        },
        "text_header": {
          "content": "Teksti"
        }
      },
      "presets": {
        "name": "Blogipostaukset"
      }
    },
    "featured-collection": {
      "name": "Esittelykokoelma",
      "settings": {
        "title": {
          "label": "Otsikko",
          "default": "Esittelykokoelma"
        },
        "collection": {
          "label": "Kokoelma"
        },
        "products_to_show": {
          "label": "Tuotemäärä"
        },
        "show_view_all": {
          "label": "Näytä kaikki ‑painike",
          "info": "Näkyvissä, jos kokoelmassa on näytettyä enemmän tuotteita"
        },
        "header": {
          "content": "Tuotekortti"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Myyjä"
        },
        "show_rating": {
          "label": "Tuotearvio",
          "info": "Arviot edellyttävät sovellusta. [Lue lisää:](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "description": {
          "label": "Kuvaus"
        },
        "show_description": {
          "label": "Näytä kokoelman kuvaus Administa"
        },
        "description_style": {
          "label": "Kuvauksen tyyli",
          "options__1": {
            "label": "Leipäteksti"
          },
          "options__2": {
            "label": "Alaotsikko"
          },
          "options__3": {
            "label": "Isot kirjaimet"
          }
        },
        "view_all_style": {
          "label": "”Katso kaikki” tyylit",
          "options__1": {
            "label": "Linkki"
          },
          "options__2": {
            "label": "Kehyspainike"
          },
          "options__3": {
            "label": "Peittävä painike"
          }
        },
        "enable_desktop_slider": {
          "label": "Karuselli"
        },
        "full_width": {
          "label": "Täyden leveyden tuotteet"
        },
        "header_mobile": {
          "content": "Mobiiliasettelu"
        },
        "columns_mobile": {
          "label": "Sarakkeet",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "swipe_on_mobile": {
          "label": "Karuselli"
        },
        "enable_quick_buy": {
          "label": "Pikalisäys"
        },
        "header_text": {
          "content": "Teksti"
        },
        "header_collection": {
          "content": "Kokoelma-asettelu"
        }
      },
      "presets": {
        "name": "Esittelykokoelma"
      }
    },
    "footer": {
      "name": "Alatunniste",
      "blocks": {
        "link_list": {
          "name": "Valikko",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Pikalinkit"
            },
            "menu": {
              "label": "Valikko"
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Otsikko"
            },
            "subtext": {
              "label": "Alateksti",
              "default": "<p>Jaa asiakkaillesi yhteystiedot, kaupan tiedot ja brändin sisältöä.</p>"
            }
          }
        },
        "brand_information": {
          "name": "Bränditiedot",
          "settings": {
            "paragraph": {
              "content": "Hallinnoi bränditietoja [teema-asetuksissa](/editor?context=theme&category=brand%20information)"
            },
            "show_social": {
              "label": "Somekuvakkeet",
              "info": "[Hallinnoi sometilejä](/editor?context=theme&category=social%20media)"
            }
          }
        }
      },
      "settings": {
        "newsletter_enable": {
          "label": "Sähköpostirekisteröityminen"
        },
        "newsletter_heading": {
          "label": "Otsikko",
          "default": "Tilaa sähköpostiviestejämme"
        },
        "header__1": {
          "content": "Sähköpostirekisteröityminen",
          "info": "Rekisteröitymiset lisäävät [asiakasprofiilit](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "show_social": {
          "label": "Somekuvakkeet",
          "info": "[Hallinnoi sometilejä](/editor?context=theme&category=social%20media)"
        },
        "enable_country_selector": {
          "label": "Maa-/aluevalitsin",
          "info": "[Hallinnoi maita/alueita](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Kielivalitsin",
          "info": "[Hallinnoi kieliä](/admin/settings/languages)"
        },
        "payment_enable": {
          "label": "Maksutapakuvakkeet"
        },
        "margin_top": {
          "label": "Yläreunus"
        },
        "show_policy": {
          "label": "Käytäntöjen linkit",
          "info": "[Hallinnoi käytäntöjä](/admin/settings/legal)"
        },
        "header__9": {
          "content": "Apuohjelmat"
        },
        "enable_follow_on_shop": {
          "label": "Seuraa Shopissa",
          "info": "Shop Payn täytyy olla käytössä [Lue lisää:](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"
        }
      }
    },
    "header": {
      "name": "Ylätunniste",
      "settings": {
        "logo_position": {
          "label": "Logon sijainti",
          "options__1": {
            "label": "Keskellä vasemmalla"
          },
          "options__2": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__3": {
            "label": "Keskellä ylhäällä"
          },
          "options__4": {
            "label": "Keskellä"
          }
        },
        "menu": {
          "label": "Valikko"
        },
        "show_line_separator": {
          "label": "Erotinviiva"
        },
        "margin_bottom": {
          "label": "Alareunus"
        },
        "menu_type_desktop": {
          "label": "Valikkotyyppi",
          "options__1": {
            "label": "Pudotusvalikko"
          },
          "options__2": {
            "label": "Mega-valikko"
          },
          "options__3": {
            "label": "Laatikko"
          }
        },
        "mobile_logo_position": {
          "label": "Logon asettelu mobiilissa",
          "options__1": {
            "label": "Keskitetty"
          },
          "options__2": {
            "label": "Vasen"
          }
        },
        "logo_help": {
          "content": "Muokkaa logoa [teeman asetuksissa](/editor?context=theme&category=logo)"
        },
        "sticky_header_type": {
          "label": "Paikallaan pysyvä ylätunniste",
          "options__1": {
            "label": "Ei mitään"
          },
          "options__2": {
            "label": "Ylöspäin vieritettäessä"
          },
          "options__3": {
            "label": "Aina"
          },
          "options__4": {
            "label": "Aina, pienennä logon kokoa"
          }
        },
        "enable_country_selector": {
          "label": "Maa-/aluevalitsin",
          "info": "[Hallinnoi maita/alueita](/admin/settings/markets)"
        },
        "enable_language_selector": {
          "label": "Kielivalitsin",
          "info": "[Hallinnoi kieliä](/admin/settings/languages)"
        },
        "header__1": {
          "content": "Väri"
        },
        "menu_color_scheme": {
          "label": "Värimallivalikko"
        },
        "enable_customer_avatar": {
          "label": "Asiakastilin avatar",
          "info": "Näkyvissä vain, kun asiakkaat ovat kirjautuneet Shopiin. [Hallinnoi asiakastilejä](/admin/settings/customer_accounts)"
        },
        "header__utilities": {
          "content": "Apuohjelmat"
        }
      }
    },
    "image-banner": {
      "name": "Kuvabanneri",
      "settings": {
        "image": {
          "label": "Kuva 1"
        },
        "image_2": {
          "label": "Kuva 2"
        },
        "stack_images_on_mobile": {
          "label": "Pinoa kuvat"
        },
        "show_text_box": {
          "label": "Säiliö"
        },
        "image_overlay_opacity": {
          "label": "Peittokuvan läpikuultavuus"
        },
        "show_text_below": {
          "label": "Säiliö"
        },
        "image_height": {
          "label": "Korkeus",
          "options__1": {
            "label": "Mukauta ensimmäisen kuvan mukaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          }
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__2": {
            "label": "Keskellä ylhäällä"
          },
          "options__3": {
            "label": "Ylhäällä oikealla"
          },
          "options__4": {
            "label": "Keskellä vasemmalla"
          },
          "options__5": {
            "label": "Keskitetty keskelle"
          },
          "options__6": {
            "label": "Keskellä oikealla"
          },
          "options__7": {
            "label": "Alhaalla vasemmalla"
          },
          "options__8": {
            "label": "Keskellä alhaalla"
          },
          "options__9": {
            "label": "Alhaalla oikealla"
          },
          "label": "Sijainti"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Tasaus"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Tasaus"
        },
        "mobile": {
          "content": "Mobiiliasettelu"
        },
        "content": {
          "content": "Sisältö"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Kuvabanneri"
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Anna asiakkaille tietoa bannerin kuvista tai sisällöstä mallissa."
            },
            "text_style": {
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              },
              "label": "Tyyli"
            }
          }
        },
        "buttons": {
          "name": "Painikkeet",
          "settings": {
            "button_label_1": {
              "label": "Merkintä",
              "info": "Jos haluat piilottaa, jätä tyhjäksi",
              "default": "Tekstipainike"
            },
            "button_link_1": {
              "label": "Linkki"
            },
            "button_style_secondary_1": {
              "label": "Ääriviivatyyli"
            },
            "button_label_2": {
              "label": "Merkintä",
              "info": "Jos haluat piilottaa, jätä tyhjäksi",
              "default": "Tekstipainike"
            },
            "button_link_2": {
              "label": "Linkki"
            },
            "button_style_secondary_2": {
              "label": "Ääriviivatyyli"
            },
            "header_1": {
              "content": "Painike 1"
            },
            "header_2": {
              "content": "Painike 2"
            }
          }
        }
      },
      "presets": {
        "name": "Kuvabanneri"
      }
    },
    "image-with-text": {
      "name": "Kuva tekstillä",
      "settings": {
        "image": {
          "label": "Kuva"
        },
        "height": {
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "label": "Korkeus",
          "options__4": {
            "label": "Suuri"
          }
        },
        "layout": {
          "options__1": {
            "label": "Kuva ensin"
          },
          "options__2": {
            "label": "Toinen kuva"
          },
          "label": "Sijoitus"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Pieni"
          },
          "options__2": {
            "label": "Keskisuuri"
          },
          "options__3": {
            "label": "Suuri"
          },
          "label": "Leveys"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Tasaus"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Ylös"
          },
          "options__2": {
            "label": "Keskelle"
          },
          "options__3": {
            "label": "Alas"
          },
          "label": "Sijainti"
        },
        "content_layout": {
          "options__1": {
            "label": "Ei päällekkäisyyksiä"
          },
          "options__2": {
            "label": "Päällekkäisyys"
          },
          "label": "Asettelu"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Mobiilitasaus"
        },
        "header": {
          "content": "Sisältö"
        },
        "header_colors": {
          "content": "Värit"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Kuva tekstillä"
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "<p>Korosta valitsemaasi tuotetta, kokoelmaa tai blogipostausta lisäämällä kuvaan teksti. Lisää tietoa saatavuudesta tai tyylistä tai näytä vaikkapa arvostelu.</p>"
            },
            "text_style": {
              "label": "Tyyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              }
            }
          }
        },
        "button": {
          "name": "Painike",
          "settings": {
            "button_label": {
              "label": "Merkintä",
              "info": "Jos haluat piilottaa, jätä tyhjäksi",
              "default": "Tekstipainike"
            },
            "button_link": {
              "label": "Linkki"
            },
            "outline_button": {
              "label": "Ääriviivatyyli"
            }
          }
        },
        "caption": {
          "name": "Kuvateksti",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Lisää iskulause"
            },
            "text_style": {
              "label": "Tyyli",
              "options__1": {
                "label": "Alaotsikko"
              },
              "options__2": {
                "label": "Isot kirjaimet"
              }
            },
            "caption_size": {
              "label": "Koko",
              "options__1": {
                "label": "Pieni"
              },
              "options__2": {
                "label": "Keskisuuri"
              },
              "options__3": {
                "label": "Suuri"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Kuva tekstillä"
      }
    },
    "main-article": {
      "name": "Blogipostaus",
      "blocks": {
        "featured_image": {
          "name": "Esittelykuva",
          "settings": {
            "image_height": {
              "label": "Kuvan korkeus",
              "options__1": {
                "label": "Sovita kuvaan"
              },
              "options__2": {
                "label": "Pieni"
              },
              "options__3": {
                "label": "Keskisuuri"
              },
              "options__4": {
                "label": "Suuri"
              }
            }
          }
        },
        "title": {
          "name": "Otsikko",
          "settings": {
            "blog_show_date": {
              "label": "Päivämäärä"
            },
            "blog_show_author": {
              "label": "Tekijä"
            }
          }
        },
        "content": {
          "name": "Sisältö"
        },
        "share": {
          "name": "Jaa",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Jaa"
            }
          }
        }
      }
    },
    "main-blog": {
      "name": "Blogipostaukset",
      "settings": {
        "show_image": {
          "label": "Esittelykuva"
        },
        "show_date": {
          "label": "Päivämäärä"
        },
        "show_author": {
          "label": "Tekijä"
        },
        "layout": {
          "label": "Asettelu",
          "options__1": {
            "label": "Ruudukko"
          },
          "options__2": {
            "label": "Kollaasi"
          }
        },
        "image_height": {
          "label": "Kuvan korkeus",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          }
        }
      }
    },
    "main-cart-footer": {
      "name": "Välisumma",
      "blocks": {
        "subtotal": {
          "name": "Välisumma yhteensä"
        },
        "buttons": {
          "name": "Kassapainike"
        }
      }
    },
    "main-cart-items": {
      "name": "Tuotteet"
    },
    "main-collection-banner": {
      "name": "Kokoelmabanneri",
      "settings": {
        "paragraph": {
          "content": "Kokoelman tietoja [hallinnoidaan Adminissa](https://help.shopify.com/manual/products/collections/collection-layout)"
        },
        "show_collection_description": {
          "label": "Kuvaus"
        },
        "show_collection_image": {
          "label": "Kuva"
        }
      }
    },
    "main-collection-product-grid": {
      "name": "Tuoteruudukko",
      "settings": {
        "products_per_page": {
          "label": "Tuotteita sivulla"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Myyjä"
        },
        "enable_tags": {
          "label": "Suodattimet",
          "info": "Mukauta suodattimia [Search & Discovery ‑sovelluksella](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_filtering": {
          "label": "Suodattimet",
          "info": "Mukauta suodattimia [Search & Discovery ‑sovelluksella](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"
        },
        "enable_sorting": {
          "label": "Lajittelu"
        },
        "header__1": {
          "content": "Suodatus ja lajittelu"
        },
        "header__3": {
          "content": "Tuotekortti"
        },
        "show_rating": {
          "label": "Tuotearvio",
          "info": "Tuotearviot edellyttävät sovellusta. [Lue lisää:](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "columns_mobile": {
          "label": "Mobiilisarakkeet",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "filter_type": {
          "label": "Suodatinasettelu",
          "options__1": {
            "label": "Vaaka"
          },
          "options__2": {
            "label": "Pysty"
          },
          "options__3": {
            "label": "Laatikko"
          }
        },
        "quick_add": {
          "label": "Pikalisäys",
          "options": {
            "option_1": "Ei ole",
            "option_2": "Vakio",
            "option_3": "Erä"
          }
        }
      }
    },
    "main-list-collections": {
      "name": "Kokoelmaluettelosivu",
      "settings": {
        "title": {
          "label": "Otsikko",
          "default": "Kokoelmat"
        },
        "sort": {
          "label": "Lajittele kokoelmat",
          "options__1": {
            "label": "Aakkosjärjestyksessä A–Ö"
          },
          "options__2": {
            "label": "Aakkosjärjestyksessä Ö–A"
          },
          "options__3": {
            "label": "Päivämäärä uusimmasta vanhimpaan"
          },
          "options__4": {
            "label": "Päivämäärä vanhimmasta uusimpaan"
          },
          "options__5": {
            "label": "Tuotteiden määrä suurimmasta pienimpään"
          },
          "options__6": {
            "label": "Tuotteiden määrä pienimmästä suurimpaan"
          }
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "header_mobile": {
          "content": "Mobiiliasettelu"
        },
        "columns_mobile": {
          "label": "Mobiilisarakkeet",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "main-page": {
      "name": "Sivu"
    },
    "main-password-footer": {
      "name": "Salasana-alatunniste"
    },
    "main-password-header": {
      "name": "Salasanaylätunniste",
      "settings": {
        "logo_help": {
          "content": "Muokkaa logoa [teeman asetuksissa](/editor?context=theme&category=logo)"
        }
      }
    },
    "main-product": {
      "blocks": {
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Tekstilohko"
            },
            "text_style": {
              "label": "Tyyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        },
        "title": {
          "name": "Otsikko"
        },
        "price": {
          "name": "Hinta"
        },
        "quantity_selector": {
          "name": "Määrän valitsin"
        },
        "variant_picker": {
          "name": "Versionvalitsin",
          "settings": {
            "picker_type": {
              "label": "Tyyli",
              "options__1": {
                "label": "Pudotusvalikko"
              },
              "options__2": {
                "label": "Pillerit"
              }
            },
            "swatch_shape": {
              "label": "Väriruutu",
              "info": "Lue lisää tuotevaihtoehtojen [väriruuduista](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches)",
              "options__1": {
                "label": "Ympyrä"
              },
              "options__2": {
                "label": "Neliö"
              },
              "options__3": {
                "label": "Ei mitään"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Osta-painikkeet",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynaamiset kassapainikkeet",
              "info": "Asiakkaat näkevät ensisijaisen maksutapansa. [Lue lisää:](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            },
            "show_gift_card_recipient": {
              "label": " Lahjakortin lähetysasetukset",
              "info": "Asiakkaat voivat lisätä henkilökohtaisen viestin ja sopia lähetyspäivän. [Lue lisää:](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"
            }
          }
        },
        "pickup_availability": {
          "name": "Noudon saatavuus"
        },
        "description": {
          "name": "Kuvaus"
        },
        "share": {
          "name": "Jaa",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Jaa"
            }
          }
        },
        "collapsible_tab": {
          "name": "Pienenettävä rivi",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Pienenettävä rivi"
            },
            "content": {
              "label": "Rivin sisältö"
            },
            "page": {
              "label": "Rivin sisältö sivulta"
            },
            "icon": {
              "options__1": {
                "label": "Ei mitään"
              },
              "options__2": {
                "label": "Omena"
              },
              "options__3": {
                "label": "Banaani"
              },
              "options__4": {
                "label": "Pullo"
              },
              "options__5": {
                "label": "Laatikko"
              },
              "options__6": {
                "label": "Porkkana"
              },
              "options__7": {
                "label": "Keskustelukupla"
              },
              "options__8": {
                "label": "Valintamerkki"
              },
              "options__9": {
                "label": "Leikepöytä"
              },
              "options__10": {
                "label": "Maitotuote"
              },
              "options__11": {
                "label": "Maidoton"
              },
              "options__12": {
                "label": "Kuivain"
              },
              "options__13": {
                "label": "Silmä"
              },
              "options__14": {
                "label": "Tuli"
              },
              "options__15": {
                "label": "Gluteeniton"
              },
              "options__16": {
                "label": "Sydän"
              },
              "options__17": {
                "label": "Silitysrauta"
              },
              "options__18": {
                "label": "Lehti"
              },
              "options__19": {
                "label": "Nahka"
              },
              "options__20": {
                "label": "Salama"
              },
              "options__21": {
                "label": "Huulipuna"
              },
              "options__22": {
                "label": "Lukko"
              },
              "options__23": {
                "label": "Karttamerkki"
              },
              "options__24": {
                "label": "Pähkinätön"
              },
              "label": "Kuvake",
              "options__25": {
                "label": "Housut"
              },
              "options__26": {
                "label": "Tassun jälki"
              },
              "options__27": {
                "label": "Pippuri"
              },
              "options__28": {
                "label": "Tuoksu"
              },
              "options__29": {
                "label": "Lentokone"
              },
              "options__30": {
                "label": "Kasvi"
              },
              "options__31": {
                "label": "Hintalappu"
              },
              "options__32": {
                "label": "Kysymysmerkki"
              },
              "options__33": {
                "label": "Kierrätys"
              },
              "options__34": {
                "label": "Palaa"
              },
              "options__35": {
                "label": "Viivain"
              },
              "options__36": {
                "label": "Tarjoiluastia"
              },
              "options__37": {
                "label": "Paita"
              },
              "options__38": {
                "label": "Kenkä"
              },
              "options__39": {
                "label": "Siluetti"
              },
              "options__40": {
                "label": "Lumihiutale"
              },
              "options__41": {
                "label": "Tähti"
              },
              "options__42": {
                "label": "Sekuntikello"
              },
              "options__43": {
                "label": "Kuljetusajoneuvo"
              },
              "options__44": {
                "label": "Pesu"
              }
            }
          }
        },
        "popup": {
          "name": "Ponnahdusikkuna",
          "settings": {
            "link_label": {
              "label": "Linkin teksti",
              "default": "Ponnahduslinkin teksti"
            },
            "page": {
              "label": "Sivu"
            }
          }
        },
        "rating": {
          "name": "Tuotearvio",
          "settings": {
            "paragraph": {
              "content": "Tuotearviot edellyttävät sovellusta. [Lue lisää:](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"
            }
          }
        },
        "complementary_products": {
          "name": "Täydentävät tuotteet",
          "settings": {
            "paragraph": {
              "content": "Hallinnoi täydentäviä tuotteita [Search & Discovery ‑sovelluksessa](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"
            },
            "heading": {
              "label": "Otsikko",
              "default": "Sopii yhteen seuraavien kanssa"
            },
            "make_collapsible_row": {
              "label": "Pienennettävä rivi"
            },
            "icon": {
              "info": "Näytetään, kun pienennettävä rivi valitaan"
            },
            "product_list_limit": {
              "label": "Tuotemäärä"
            },
            "products_per_page": {
              "label": "Tuotteita sivulla"
            },
            "pagination_style": {
              "label": "Sivunumerointi",
              "options": {
                "option_1": "Pisteet",
                "option_2": "Laskuri",
                "option_3": "Numerot"
              }
            },
            "product_card": {
              "heading": "Tuotekortti"
            },
            "image_ratio": {
              "label": "Kuvasuhde",
              "options": {
                "option_1": "Muotokuva",
                "option_2": "Neliö"
              }
            },
            "enable_quick_add": {
              "label": "Pikalisäys"
            }
          }
        },
        "icon_with_text": {
          "name": "Kuvake johon liittyy teksti",
          "settings": {
            "layout": {
              "label": "Asettelu",
              "options__1": {
                "label": "Vaaka"
              },
              "options__2": {
                "label": "Pysty"
              }
            },
            "heading": {
              "info": "Jos haluat piilottaa tämän parin, jätä tyhjäksi"
            },
            "icon_1": {
              "label": "Kuvake"
            },
            "image_1": {
              "label": "Kuva"
            },
            "heading_1": {
              "label": "Otsikko",
              "default": "Otsikko"
            },
            "icon_2": {
              "label": "Kuvake"
            },
            "image_2": {
              "label": "Kuva"
            },
            "heading_2": {
              "label": "Otsikko",
              "default": "Otsikko"
            },
            "icon_3": {
              "label": "Kuvake"
            },
            "image_3": {
              "label": "Kuva"
            },
            "heading_3": {
              "label": "Otsikko",
              "default": "Otsikko"
            },
            "pairing_1": {
              "label": "Pari 1",
              "info": "Valitse kuvake tai lisää kuva jokaiselle parille"
            },
            "pairing_2": {
              "label": "Pari 2"
            },
            "pairing_3": {
              "label": "Pari 3"
            }
          }
        },
        "sku": {
          "name": "SKU-koodi",
          "settings": {
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        },
        "inventory": {
          "name": "Varaston tila",
          "settings": {
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            },
            "inventory_threshold": {
              "label": "Vähäisen varaston kynnysarvo"
            },
            "show_inventory_quantity": {
              "label": "Varastomäärä"
            }
          }
        }
      },
      "settings": {
        "header": {
          "content": "Media"
        },
        "enable_video_looping": {
          "label": "Jatkuvasti toistuva video"
        },
        "enable_sticky_info": {
          "label": "Kiinnitetty sisältö"
        },
        "hide_variants": {
          "label": "Piilota toinen mediaversio, kun yksi valitaan"
        },
        "gallery_layout": {
          "label": "Asettelu",
          "options__1": {
            "label": "Päällekkäin"
          },
          "options__2": {
            "label": "2 saraketta"
          },
          "options__3": {
            "label": "Pikkukuvat"
          },
          "options__4": {
            "label": "Pikkukuvien karuselli"
          }
        },
        "media_size": {
          "label": "Leveys",
          "options__1": {
            "label": "Pieni"
          },
          "options__2": {
            "label": "Keskisuuri"
          },
          "options__3": {
            "label": "Suuri"
          }
        },
        "mobile_thumbnails": {
          "label": "Mobiiliasettelu",
          "options__1": {
            "label": "2 saraketta"
          },
          "options__2": {
            "label": "Näytä pikkukuvat"
          },
          "options__3": {
            "label": "Piilota pikkukuvat"
          }
        },
        "media_position": {
          "label": "Sijainti",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Oikea"
          }
        },
        "image_zoom": {
          "label": "Lähennä",
          "options__1": {
            "label": "Avaa lightbox-ikkuna"
          },
          "options__2": {
            "label": "Klikkaa ja vie osoitin päälle"
          },
          "options__3": {
            "label": "Ei zoomausta"
          }
        },
        "constrain_to_viewport": {
          "label": "Sovita näytön korkeuteen"
        },
        "media_fit": {
          "label": "Sovita",
          "options__1": {
            "label": "Alkuperäinen"
          },
          "options__2": {
            "label": "Täyttö"
          }
        }
      },
      "name": "Tuotetiedot"
    },
    "main-search": {
      "name": "Hakutulokset",
      "settings": {
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Myyjä"
        },
        "header__1": {
          "content": "Tuotekortti"
        },
        "header__2": {
          "content": "Blogikortti"
        },
        "article_show_date": {
          "label": "Päivämäärä"
        },
        "article_show_author": {
          "label": "Tekijä"
        },
        "show_rating": {
          "label": "Tuotearvio",
          "info": "Tuotearviot edellyttävät sovellusta. [Lue lisää:](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "columns_mobile": {
          "label": "Mobiilisarakkeet",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multicolumn": {
      "name": "Monisarakkeinen",
      "settings": {
        "title": {
          "label": "Otsikko",
          "default": "Monisarakkeinen"
        },
        "image_width": {
          "label": "Leveys",
          "options__1": {
            "label": "Kolmasosa sarakkeen leveydestä"
          },
          "options__2": {
            "label": "Puolet sarakkeen leveydestä"
          },
          "options__3": {
            "label": "Sarakkeen levyinen"
          }
        },
        "image_ratio": {
          "label": "Suhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          },
          "options__4": {
            "label": "Ympyrä"
          }
        },
        "column_alignment": {
          "label": "Sarakkeen tasaus",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          }
        },
        "background_style": {
          "label": "Toissijainen tausta",
          "options__1": {
            "label": "Ei mitään"
          },
          "options__2": {
            "label": "Näytä sarakkeen taustana"
          }
        },
        "button_label": {
          "label": "Merkintä",
          "default": "Tekstipainike",
          "info": "Jos haluat piilottaa, jätä tyhjäksi"
        },
        "button_link": {
          "label": "Linkki"
        },
        "swipe_on_mobile": {
          "label": "Karuselli"
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "header_mobile": {
          "content": "Mobiiliasettelu"
        },
        "columns_mobile": {
          "label": "Sarakkeet",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "header_text": {
          "content": "Otsikko"
        },
        "header_image": {
          "content": "Kuva"
        },
        "header_layout": {
          "content": "Asettelu"
        },
        "header_button": {
          "content": "Painike"
        }
      },
      "blocks": {
        "column": {
          "name": "Sarake",
          "settings": {
            "image": {
              "label": "Kuva"
            },
            "title": {
              "label": "Otsikko",
              "default": "Sarake"
            },
            "text": {
              "label": "Kuvaus",
              "default": "<p>Korosta valitsemaasi tuotetta, kokoelmaa tai blogipostausta lisäämällä kuvaan teksti. Lisää tietoa saatavuudesta tai tyylistä tai näytä vaikkapa arvostelu.</p>"
            },
            "link_label": {
              "label": "Linkin teksti",
              "info": "Jos haluat piilottaa, jätä tyhjäksi"
            },
            "link": {
              "label": "Linkki"
            }
          }
        }
      },
      "presets": {
        "name": "Monisarakkeinen"
      }
    },
    "newsletter": {
      "name": "Sähköpostirekisteröityminen",
      "settings": {
        "full_width": {
          "label": "Täysi leveys"
        },
        "paragraph": {
          "content": "Rekisteröitymiset lisäävät [asiakasprofiilit](https://help.shopify.com/manual/customers/manage-customers)"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Tilaa sähköpostiviestejämme"
            }
          }
        },
        "paragraph": {
          "name": "Teksti",
          "settings": {
            "paragraph": {
              "label": "Teksti",
              "default": "<p>Saa ensimmäisten joukossa tietoa uusista kokoelmista ja ainutlaatuisista tarjouksista.</p>"
            }
          }
        },
        "email_form": {
          "name": "Sähköpostilomake"
        }
      },
      "presets": {
        "name": "Sähköpostirekisteröityminen"
      }
    },
    "page": {
      "name": "Sivu",
      "settings": {
        "page": {
          "label": "Sivu"
        }
      },
      "presets": {
        "name": "Sivu"
      }
    },
    "rich-text": {
      "name": "Rich text",
      "settings": {
        "full_width": {
          "label": "Täysi leveys"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Sisällön sijainti"
        },
        "content_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Sisällön kohdistus"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Kerro brändistäsi"
            }
          }
        },
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "<p>Kerro brändiäsi koskevia tietoja asiakkaillesi. Kuvaile tuotetta, jaa ilmoituksia tai toivota asiakkaat tervetulleiksi kauppaasi.</p>"
            }
          }
        },
        "buttons": {
          "name": "Painikkeet",
          "settings": {
            "button_label_1": {
              "label": "Merkintä",
              "info": "Jos haluat piilottaa, jätä tyhjäksi",
              "default": "Tekstipainike"
            },
            "button_link_1": {
              "label": "Linkki"
            },
            "button_style_secondary_1": {
              "label": "Ääriviivatyyli"
            },
            "button_label_2": {
              "label": "Merkintä",
              "info": "Jos haluat piilottaa, jätä merkintä tyhjäksi"
            },
            "button_link_2": {
              "label": "Linkki"
            },
            "button_style_secondary_2": {
              "label": "Ääriviivatyyli"
            },
            "header_button1": {
              "content": "Painike 1"
            },
            "header_button2": {
              "content": "Painike 2"
            }
          }
        },
        "caption": {
          "name": "Kuvateksti",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Lisää iskulause"
            },
            "text_style": {
              "label": "Tyyli",
              "options__1": {
                "label": "Alaotsikko"
              },
              "options__2": {
                "label": "Isot kirjaimet"
              }
            },
            "caption_size": {
              "label": "Koko",
              "options__1": {
                "label": "Pieni"
              },
              "options__2": {
                "label": "Keskisuuri"
              },
              "options__3": {
                "label": "Suuri"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Rich text"
      }
    },
    "apps": {
      "name": "Sovellukset",
      "settings": {
        "include_margins": {
          "label": "Tee osioiden reunuksista sama kuin teema"
        }
      },
      "presets": {
        "name": "Sovellukset"
      }
    },
    "video": {
      "name": "Video",
      "settings": {
        "heading": {
          "label": "Otsikko",
          "default": "Video"
        },
        "cover_image": {
          "label": "Kansikuva"
        },
        "video_url": {
          "label": "URL-osoite",
          "info": "Käytä YouTube- tai Vimeo-linkkiä"
        },
        "description": {
          "label": "Videon vaihtoehtoinen teksti",
          "info": "Kuvaile videota näytönlukijoita käyttäville"
        },
        "image_padding": {
          "label": "Lisää kuvan täyttö",
          "info": "Valitse kuvan täyttäminen, jos et halua, että kansikuvaasi rajataan."
        },
        "full_width": {
          "label": "Täysi leveys"
        },
        "video": {
          "label": "Video"
        },
        "enable_video_looping": {
          "label": "Jatkuvasti toistuva video"
        },
        "header__1": {
          "content": "Shopifyn isännöimä video"
        },
        "header__2": {
          "content": "Tai sulauta video URL-osoitteesta"
        },
        "header__3": {
          "content": "Asettelu"
        },
        "paragraph": {
          "content": "Näytetään, kun mitään Shopifyssä isännöityä videota ei ole valittu"
        }
      },
      "presets": {
        "name": "Video"
      }
    },
    "featured-product": {
      "name": "Esittelyssä oleva tuote",
      "blocks": {
        "text": {
          "name": "Teksti",
          "settings": {
            "text": {
              "label": "Teksti",
              "default": "Tekstilohko"
            },
            "text_style": {
              "label": "Tyyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        },
        "title": {
          "name": "Otsikko"
        },
        "price": {
          "name": "Hinta"
        },
        "quantity_selector": {
          "name": "Määrän valitsin"
        },
        "variant_picker": {
          "name": "Versionvalitsin",
          "settings": {
            "picker_type": {
              "label": "Tyyli",
              "options__1": {
                "label": "Pudotusvalikko"
              },
              "options__2": {
                "label": "Kuvakkeet"
              }
            },
            "swatch_shape": {
              "label": "Väriruutu",
              "info": "Lue lisää tuotevaihtoehtojen [väriruuduista](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches)",
              "options__1": {
                "label": "Ympyrä"
              },
              "options__2": {
                "label": "Neliö"
              },
              "options__3": {
                "label": "Ei mitään"
              }
            }
          }
        },
        "buy_buttons": {
          "name": "Osta-painikkeet",
          "settings": {
            "show_dynamic_checkout": {
              "label": "Dynaamiset kassapainikkeet",
              "info": "Asiakkaat näkevät ensisijaisen maksutapansa. [Lue lisää:](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
            }
          }
        },
        "description": {
          "name": "Kuvaus"
        },
        "share": {
          "name": "Jaa",
          "settings": {
            "featured_image_info": {
              "content": "Jos lisäät sosiaalisen median julkaisuihin linkkejä, esikatselukuvana näkyy sivun esittelykuva. [Lisätietoja](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"
            },
            "title_info": {
              "content": "Esikatselukuvassa näkyy kaupan nimi ja kuvaus. [Lisätietoja](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"
            },
            "text": {
              "label": "Teksti",
              "default": "Jaa"
            }
          }
        },
        "rating": {
          "name": "Tuotearvio",
          "settings": {
            "paragraph": {
              "content": "Tuotearviot edellyttävät sovellusta. [Lue lisää:](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"
            }
          }
        },
        "sku": {
          "name": "SKU-koodi",
          "settings": {
            "text_style": {
              "label": "Tekstityyli",
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "options__3": {
                "label": "Isot kirjaimet"
              }
            }
          }
        }
      },
      "settings": {
        "product": {
          "label": "Tuote"
        },
        "secondary_background": {
          "label": "Toissijainen tausta"
        },
        "header": {
          "content": "Media"
        },
        "enable_video_looping": {
          "label": "Jatkuvasti toistuva video"
        },
        "hide_variants": {
          "label": "Piilota valitsemattomien versioiden media tietokoneversiossa"
        },
        "media_position": {
          "label": "Sijainti",
          "info": "Sijainti optimoidaan automaattisesti mobiililaitteille.",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Oikea"
          }
        }
      },
      "presets": {
        "name": "Esittelyssä oleva tuote"
      }
    },
    "email-signup-banner": {
      "name": "Sähköpostirekisteröitymisen banneri",
      "settings": {
        "paragraph": {
          "content": "Rekisteröitymiset lisäävät [asiakasprofiilit](https://help.shopify.com/manual/customers/manage-customers)"
        },
        "image": {
          "label": "Taustakuva"
        },
        "show_background_image": {
          "label": "Näytä taustakuva"
        },
        "show_text_box": {
          "label": "Säiliö"
        },
        "image_overlay_opacity": {
          "label": "Peittokuvan läpikuultavuus"
        },
        "show_text_below": {
          "label": "Pinoa teksti kuvan alle"
        },
        "image_height": {
          "label": "Korkeus",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          }
        },
        "desktop_content_position": {
          "options__4": {
            "label": "Keskellä vasemmalla"
          },
          "options__5": {
            "label": "Keskitetty keskelle"
          },
          "options__6": {
            "label": "Keskellä oikealla"
          },
          "options__7": {
            "label": "Alhaalla vasemmalla"
          },
          "options__8": {
            "label": "Keskellä alhaalla"
          },
          "options__9": {
            "label": "Alhaalla oikealla"
          },
          "options__1": {
            "label": "Ylhäällä vasemmalla"
          },
          "options__2": {
            "label": "Keskellä ylhäällä"
          },
          "options__3": {
            "label": "Ylhäällä oikealla"
          },
          "label": "Sijainti"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Tasaus"
        },
        "header": {
          "content": "Mobiiliasettelu"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasemmalla"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikealla"
          },
          "label": "Tasaus"
        },
        "color_scheme": {
          "info": "Näkyvissä, kun säilö on esillä."
        },
        "content_header": {
          "content": "Sisältö"
        }
      },
      "blocks": {
        "heading": {
          "name": "Otsikko",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Avataan pian"
            }
          }
        },
        "paragraph": {
          "name": "Teksti",
          "settings": {
            "paragraph": {
              "label": "Teksti",
              "default": "<p>Saa tietoa avaamisesta ensimmäisten joukossa.</p>"
            },
            "text_style": {
              "options__1": {
                "label": "Leipäteksti"
              },
              "options__2": {
                "label": "Alaotsikko"
              },
              "label": "Tyyli"
            }
          }
        },
        "email_form": {
          "name": "Sähköpostilomake"
        }
      },
      "presets": {
        "name": "Sähköpostirekisteröitymisen banneri"
      }
    },
    "slideshow": {
      "name": "Diaesitys",
      "settings": {
        "layout": {
          "label": "Asettelu",
          "options__1": {
            "label": "Täysi leveys"
          },
          "options__2": {
            "label": "Sivu"
          }
        },
        "slide_height": {
          "label": "Korkeus",
          "options__1": {
            "label": "Mukauta ensimmäisen kuvan mukaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          }
        },
        "slider_visual": {
          "label": "Sivunumerointi",
          "options__1": {
            "label": "Laskuri"
          },
          "options__2": {
            "label": "Pisteet"
          },
          "options__3": {
            "label": "Numerot"
          }
        },
        "auto_rotate": {
          "label": "Käännä diat automaattisesti"
        },
        "change_slides_speed": {
          "label": "Vaihda diat joka"
        },
        "show_text_below": {
          "label": "Pinoa teksti kuvan alle"
        },
        "mobile": {
          "content": "Mobiiliasettelu"
        },
        "accessibility": {
          "content": "Käytettävyys",
          "label": "Diaesityksen kuvaus",
          "info": "Kuvaile diaesitystä näytönlukijoita käyttäville",
          "default": "Diaesitys brändistäsi"
        }
      },
      "blocks": {
        "slide": {
          "name": "Dia",
          "settings": {
            "image": {
              "label": "Kuva"
            },
            "heading": {
              "label": "Otsikko",
              "default": "Kuvadia"
            },
            "subheading": {
              "label": "Alaotsikko",
              "default": "Kerro brändisi tarina kuvilla"
            },
            "button_label": {
              "label": "Merkintä",
              "info": "Jos haluat piilottaa, jätä tyhjäksi",
              "default": "Tekstipainike"
            },
            "link": {
              "label": "Linkki"
            },
            "secondary_style": {
              "label": "Ääriviivatyyli"
            },
            "box_align": {
              "label": "Sisällön sijainti",
              "options__1": {
                "label": "Ylhäällä vasemmalla"
              },
              "options__2": {
                "label": "Keskellä ylhäällä"
              },
              "options__3": {
                "label": "Ylhäällä oikealla"
              },
              "options__4": {
                "label": "Keskellä vasemmalla"
              },
              "options__5": {
                "label": "Keskellä"
              },
              "options__6": {
                "label": "Keskellä oikealla"
              },
              "options__7": {
                "label": "Alhaalla vasemmalla"
              },
              "options__8": {
                "label": "Keskellä alhaalla"
              },
              "options__9": {
                "label": "Alhaalla oikealla"
              }
            },
            "show_text_box": {
              "label": "Säiliö"
            },
            "text_alignment": {
              "label": "Sisällön kohdistus",
              "option_1": {
                "label": "Vasemmalla"
              },
              "option_2": {
                "label": "Keskitetty"
              },
              "option_3": {
                "label": "Oikealla"
              }
            },
            "image_overlay_opacity": {
              "label": "Peittokuvan läpikuultavuus"
            },
            "text_alignment_mobile": {
              "label": "Mobiilisisällön tasaus",
              "options__1": {
                "label": "Vasemmalla"
              },
              "options__2": {
                "label": "Keskitetty"
              },
              "options__3": {
                "label": "Oikealla"
              }
            },
            "header_button": {
              "content": "Painike"
            },
            "header_layout": {
              "content": "Asettelu"
            },
            "header_text": {
              "content": "Teksti"
            },
            "header_colors": {
              "content": "Värit"
            }
          }
        }
      },
      "presets": {
        "name": "Diaesitys"
      }
    },
    "collapsible_content": {
      "name": "Pienenettävä sisältö",
      "settings": {
        "caption": {
          "label": "Kuvateksti"
        },
        "heading": {
          "label": "Otsikko",
          "default": "Pienenettävä sisältö"
        },
        "heading_alignment": {
          "label": "Otsikon tasaus",
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          }
        },
        "layout": {
          "label": "Säiliö",
          "options__1": {
            "label": "Ei säiliötä"
          },
          "options__2": {
            "label": "Rivisäiliö"
          },
          "options__3": {
            "label": "Osiosäiliö"
          }
        },
        "open_first_collapsible_row": {
          "label": "Avaa ensimmäinen rivi"
        },
        "header": {
          "content": "Kuva"
        },
        "image": {
          "label": "Kuva"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Suuri"
          }
        },
        "desktop_layout": {
          "label": "Sijoitus",
          "options__1": {
            "label": "Ensimmäinen kuva"
          },
          "options__2": {
            "label": "Toinen kuva"
          }
        },
        "container_color_scheme": {
          "label": "Säiliön värimalli"
        },
        "layout_header": {
          "content": "Asettelu"
        },
        "section_color_scheme": {
          "label": "Osion värimalli"
        }
      },
      "blocks": {
        "collapsible_row": {
          "name": "Pienenettävä rivi",
          "settings": {
            "heading": {
              "label": "Otsikko",
              "default": "Pienenettävä rivi"
            },
            "row_content": {
              "label": "Rivin sisältö"
            },
            "page": {
              "label": "Rivin sisältö sivulta"
            },
            "icon": {
              "label": "Kuvake",
              "options__1": {
                "label": "Ei yhtään"
              },
              "options__2": {
                "label": "Omena"
              },
              "options__3": {
                "label": "Banaani"
              },
              "options__4": {
                "label": "Pullo"
              },
              "options__5": {
                "label": "Laatikko"
              },
              "options__6": {
                "label": "Porkkana"
              },
              "options__7": {
                "label": "Keskustelukupla"
              },
              "options__8": {
                "label": "Valintamerkki"
              },
              "options__9": {
                "label": "Leikepöytä"
              },
              "options__10": {
                "label": "Maitotuote"
              },
              "options__11": {
                "label": "Maidoton"
              },
              "options__12": {
                "label": "Kuivain"
              },
              "options__13": {
                "label": "Silmä"
              },
              "options__14": {
                "label": "Tuli"
              },
              "options__15": {
                "label": "Gluteeniton"
              },
              "options__16": {
                "label": "Sydän"
              },
              "options__17": {
                "label": "Silitysrauta"
              },
              "options__18": {
                "label": "Lehti"
              },
              "options__19": {
                "label": "Nahka"
              },
              "options__20": {
                "label": "Salama"
              },
              "options__21": {
                "label": "Huulipuna"
              },
              "options__22": {
                "label": "Lukko"
              },
              "options__23": {
                "label": "Karttamerkki"
              },
              "options__24": {
                "label": "Pähkinätön"
              },
              "options__25": {
                "label": "Housut"
              },
              "options__26": {
                "label": "Tassun jälki"
              },
              "options__27": {
                "label": "Pippuri"
              },
              "options__28": {
                "label": "Tuoksu"
              },
              "options__29": {
                "label": "Lentokone"
              },
              "options__30": {
                "label": "Kasvi"
              },
              "options__31": {
                "label": "Hintalappu"
              },
              "options__32": {
                "label": "Kysymysmerkki"
              },
              "options__33": {
                "label": "Kierrätys"
              },
              "options__34": {
                "label": "Palaa"
              },
              "options__35": {
                "label": "Viivain"
              },
              "options__36": {
                "label": "Tarjoiluastia"
              },
              "options__37": {
                "label": "Paita"
              },
              "options__38": {
                "label": "Kenkä"
              },
              "options__39": {
                "label": "Siluetti"
              },
              "options__40": {
                "label": "Lumihiutale"
              },
              "options__41": {
                "label": "Tähti"
              },
              "options__42": {
                "label": "Sekuntikello"
              },
              "options__43": {
                "label": "Kuljetusajoneuvo"
              },
              "options__44": {
                "label": "Pesu"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Pienenettävä sisältö"
      }
    },
    "main-account": {
      "name": "Tili"
    },
    "main-activate-account": {
      "name": "Tilin aktivointi"
    },
    "main-addresses": {
      "name": "Osoitteet"
    },
    "main-login": {
      "name": "Kirjautuminen",
      "shop_login_button": {
        "enable": "Ota käyttöön kirjautuminen Shopilla"
      }
    },
    "main-order": {
      "name": "Tilaus"
    },
    "main-register": {
      "name": "Rekisteröinti"
    },
    "main-reset-password": {
      "name": "Salasanan nollaus"
    },
    "related-products": {
      "name": "Liittyvät tuotteet",
      "settings": {
        "heading": {
          "label": "Otsikko"
        },
        "products_to_show": {
          "label": "Tuotemäärä"
        },
        "columns_desktop": {
          "label": "Sarakkeet"
        },
        "paragraph__1": {
          "content": "Liittyviä tuotteita voi hallinnoida [Search & Discovery ‑sovelluksessa](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)",
          "default": "Saatat pitää myös näistä"
        },
        "header__2": {
          "content": "Tuotekortti"
        },
        "image_ratio": {
          "label": "Kuvasuhde",
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Muotokuva"
          },
          "options__3": {
            "label": "Neliö"
          }
        },
        "show_secondary_image": {
          "label": "Näytä toinen kuva osoittaessa"
        },
        "show_vendor": {
          "label": "Myyjä"
        },
        "show_rating": {
          "label": "Tuotearvio",
          "info": "Tuotearviot edellyttävät sovellusta. [Lue lisää:](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"
        },
        "columns_mobile": {
          "label": "Mobiilisarakkeet",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        }
      }
    },
    "multirow": {
      "name": "Monirivinen",
      "settings": {
        "image": {
          "label": "Kuva"
        },
        "image_height": {
          "options__1": {
            "label": "Sovita kuvaan"
          },
          "options__2": {
            "label": "Pieni"
          },
          "options__3": {
            "label": "Keskisuuri"
          },
          "options__4": {
            "label": "Suuri"
          },
          "label": "Korkeus"
        },
        "desktop_image_width": {
          "options__1": {
            "label": "Pieni"
          },
          "options__2": {
            "label": "Keskisuuri"
          },
          "options__3": {
            "label": "Suuri"
          },
          "label": "Leveys"
        },
        "text_style": {
          "options__1": {
            "label": "Leipäteksti"
          },
          "options__2": {
            "label": "Alaotsikko"
          },
          "label": "Tekstityyli"
        },
        "button_style": {
          "options__1": {
            "label": "Yhtenäinen painike"
          },
          "options__2": {
            "label": "Kehyspainike"
          },
          "label": "Painikkeen tyyli"
        },
        "desktop_content_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Tasaus"
        },
        "desktop_content_position": {
          "options__1": {
            "label": "Ylös"
          },
          "options__2": {
            "label": "Keskelle"
          },
          "options__3": {
            "label": "Alas"
          },
          "label": "Sijainti"
        },
        "image_layout": {
          "options__1": {
            "label": "Vuorottelu vasemmalta"
          },
          "options__2": {
            "label": "Vuorottelu oikealta"
          },
          "options__3": {
            "label": "Tasattu vasemmalle"
          },
          "options__4": {
            "label": "Tasattu oikealle"
          },
          "label": "Sijoitus"
        },
        "container_color_scheme": {
          "label": "Säiliön värimalli"
        },
        "mobile_content_alignment": {
          "options__1": {
            "label": "Vasen"
          },
          "options__2": {
            "label": "Keskitetty"
          },
          "options__3": {
            "label": "Oikea"
          },
          "label": "Mobiilitasaus"
        },
        "header": {
          "content": "Kuva"
        },
        "header_2": {
          "content": "Sisältö"
        },
        "header_3": {
          "content": "Värit"
        }
      },
      "blocks": {
        "row": {
          "name": "Rivi",
          "settings": {
            "image": {
              "label": "Kuva"
            },
            "caption": {
              "label": "Kuvateksti",
              "default": "Kuvateksti"
            },
            "heading": {
              "label": "Otsikko",
              "default": "Rivi"
            },
            "text": {
              "label": "Teksti",
              "default": "<p>Korosta valitsemaasi tuotetta, kokoelmaa tai blogipostausta lisäämällä kuvaan teksti. Lisää tietoa saatavuudesta tai tyylistä tai näytä vaikkapa arvostelu.</p>"
            },
            "button_label": {
              "label": "Tekstipainike",
              "default": "Tekstipainike",
              "info": "Jos haluat piilottaa, jätä tyhjäksi"
            },
            "button_link": {
              "label": "Painikelinkki"
            }
          }
        }
      },
      "presets": {
        "name": "Monirivinen"
      }
    },
    "quick-order-list": {
      "name": "Pikatilausluettelo",
      "settings": {
        "show_image": {
          "label": "Kuvat"
        },
        "show_sku": {
          "label": "SKU-koodit"
        },
        "variants_per_page": {
          "label": "Versioita sivulla"
        }
      },
      "presets": {
        "name": "Pikatilausluettelo"
      }
    }
  }
}
