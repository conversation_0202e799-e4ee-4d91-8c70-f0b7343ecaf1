# Color Variants as Separate Cards Feature

## Overview

This feature allows displaying each color variant as separate cards in collection pages, instead of showing just one product per card. Only unique colors are shown as separate cards - size and other variants are grouped under each color.

## Implementation Details

### Files Modified

1. **config/settings_schema.json**
   - Added a new checkbox setting `show_variants_as_products` in the Cards section
   - Label: "Show color variants as separate cards"
   - Default: false (disabled)

2. **snippets/card-product.liquid**
   - Added support for new parameters:
     - `card_variant`: Product variant object for variant-specific display
     - `custom_url`: Custom URL to use instead of product.url
     - `custom_image`: Custom image to use instead of product.featured_media
   - Updated logic to use variant-specific data when available
   - Modified title display to show "Product Name - Color" format (color only, no size)
   - Updated price and availability checks to use variant data

3. **sections/main-collection-product-grid.liquid**
   - Added conditional logic to switch between color variant display and traditional product display
   - When `settings.show_variants_as_products` is enabled:
     - Checks if product has color options (Color, color, Colour, colour)
     - **Only creates separate cards for color variants that have their own featured images**
     - Ignores color variants without images (they won't get separate cards)
     - Loops through unique color variants only (ignores size/other variants)
     - Products without color options show as single cards
     - Products with color options but no variant images show as single cards
     - Passes variant-specific URL and image data to the card component
   - Maintains backward compatibility with traditional product display

## How to Use

1. **Enable the Feature:**
   - Go to Theme Settings in Shopify Admin
   - Navigate to the "Cards" section
   - Check "Show color variants as separate cards"
   - Save the settings

2. **Expected Behavior:**
   - Collection pages will now show each color variant as a separate card
   - Each card will display:
     - Color-specific image (if available)
     - Product title with color name (e.g., "T-Shirt - Red")
     - Color variant-specific price and availability
     - Direct link to the color variant
   - Products without color options will display as single cards

3. **Disable the Feature:**
   - Uncheck the setting to return to traditional product display
   - Each product will show as a single card regardless of variants

## Testing Recommendations

1. **Test with Multi-Color Products:**
   - Create products with multiple color variants (and optionally sizes)
   - Ensure each unique color appears as a separate card
   - Verify color-specific images display correctly
   - Confirm that size variants are grouped under each color (not shown as separate cards)

2. **Test Functionality:**
   - Click on variant cards to ensure they link to the correct variant
   - Verify pricing displays correctly for each variant
   - Check that sold-out variants show appropriate badges

3. **Test Responsiveness:**
   - Verify the layout works on mobile and desktop
   - Ensure grid spacing remains consistent

4. **Test Performance:**
   - Monitor page load times with many variants
   - Consider pagination settings if displaying many variant cards

## Example Behavior

### Product: "Cotton T-Shirt" (all variants have images)

- Variants: Red/Small, Red/Medium, Blue/Small, Blue/Large
- Each color variant has its own featured image
- **Result**: 2 cards ("Cotton T-Shirt - Red" and "Cotton T-Shirt - Blue")

### Product: "Cotton T-Shirt" (mixed - some variants have images)

- Variants: Red/Small, Red/Medium, Blue/Small, Blue/Large, Green/Small, Green/Medium
- Red and Blue variants have their own featured images
- Green variants have no featured image (would use product default)
- **Result**: 2 cards ("Cotton T-Shirt - Red" and "Cotton T-Shirt - Blue")
- Green variants are ignored (no separate card created)

### Product: "Cotton T-Shirt" (no variant images)

- Variants: Red/Small, Red/Medium, Blue/Small, Blue/Large
- No variant-specific images (all variants use product featured image)
- **Result**: 1 card (titled "Cotton T-Shirt" - traditional display)

### Product: "Basic Mug"

- Variants: Small, Medium, Large (no colors)
- **Result**: 1 card (titled "Basic Mug" - traditional display)

## Notes

- The feature maintains full backward compatibility
- All existing card styling and functionality is preserved
- Quick add functionality works with variant cards
- The implementation respects lazy loading for performance
- Product titles show color information only (e.g., "Product - Red", not "Product - Red - Small")
