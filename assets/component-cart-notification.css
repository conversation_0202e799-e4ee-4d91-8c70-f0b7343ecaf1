.cart-notification-wrapper {
  position: relative;
}

.cart-notification-wrapper .cart-notification {
  display: block;
}

.cart-notification {
  border-bottom-right-radius: var(--popup-corner-radius);
  border-bottom-left-radius: var(--popup-corner-radius);
  border-color: rgba(var(--color-foreground), var(--popup-border-opacity));
  border-style: solid;
  border-width: 0 0 var(--popup-border-width);
  padding: 2.5rem 3.5rem;
  position: absolute;
  right: 0;
  transform: translateY(-100%);
  visibility: hidden;
  width: 100%;
  box-shadow: var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius)
    rgba(var(--color-shadow), var(--popup-shadow-opacity));
  z-index: -1;
}

.cart-notification.focused {
  box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3),
    var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius)
      rgba(var(--color-shadow), var(--popup-shadow-opacity));
}

.cart-notification:focus-visible {
  box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3),
    var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius)
      rgba(var(--color-shadow), var(--popup-shadow-opacity));
}

@media screen and (min-width: 750px) {
  .header-wrapper:not(.header-wrapper--border-bottom) + cart-notification .cart-notification {
    border-top-width: var(--popup-border-width);
  }

  .cart-notification {
    border-width: 0 var(--popup-border-width) var(--popup-border-width);
    max-width: 36.8rem;
    right: 2.2rem;
  }
}

@media screen and (min-width: 990px) {
  .cart-notification-wrapper:is(.page-width) > .cart-notification {
    right: 4rem;
  }
}

.cart-notification.animate {
  transition: transform var(--duration-short) ease, visibility 0s var(--duration-short) ease;
}

.cart-notification.active {
  transform: translateY(0);
  transition: transform var(--duration-default) ease, visibility 0s;
  visibility: visible;
}

.cart-notification__header {
  align-items: flex-start;
  display: flex;
}

.cart-notification__heading {
  align-items: center;
  display: flex;
  flex-grow: 1;
  margin-bottom: 0;
  margin-top: 0;
}

.cart-notification__heading .icon-checkmark {
  color: rgb(var(--color-foreground));
  margin-right: 1rem;
  width: 1.3rem;
}

.cart-notification__close {
  margin-top: -2rem;
  margin-right: -3rem;
}

.cart-notification__links {
  text-align: center;
}

.cart-notification__links > * {
  margin-top: 1rem;
}

.cart-notification-product {
  align-items: flex-start;
  display: flex;
  padding-bottom: 3rem;
  padding-top: 2rem;
}

.cart-notification-product dl {
  margin-bottom: 0;
  margin-top: 0;
}

.cart-notification-product__image {
  display: inline-flex;
  margin-right: 1.5rem;
  margin-top: 0.5rem;
}

.cart-notification-product__image:after {
  content: none;
}

.cart-notification-product__name {
  margin-bottom: 0.5rem;
  margin-top: 0;
}
