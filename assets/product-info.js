if (!customElements.get('product-info')) {
  customElements.define(
    'product-info',
    class ProductInfo extends HTMLElement {
      quantityInput = undefined;
      quantityForm = undefined;
      onVariantChangeUnsubscriber = undefined;
      cartUpdateUnsubscriber = undefined;
      abortController = undefined;
      pendingRequestUrl = null;
      preProcessHtmlCallbacks = [];
      postProcessHtmlCallbacks = [];

      constructor() {
        super();

        this.quantityInput = this.querySelector('.quantity__input');
        this.lastSelectedThumbnailPosition = 0; // Track which thumbnail position was selected (0 = first, 1 = second, etc.)

        // Track thumbnail clicks to preserve selection across variant changes
        this.addEventListener('click', (event) => {
          const thumbnailButton = event.target.closest('.thumbnail');
          if (thumbnailButton) {
            const thumbnailItem = thumbnailButton.closest('.thumbnail-list__item');
            if (thumbnailItem) {
              // Find the position of this thumbnail in the visible thumbnail list
              const visibleThumbnails = Array.from(this.querySelectorAll('.thumbnail-list__item')).filter(thumb => {
                const style = window.getComputedStyle(thumb);
                return style.display !== 'none';
              });
              this.lastSelectedThumbnailPosition = visibleThumbnails.indexOf(thumbnailItem);
              console.log('Selected thumbnail position:', this.lastSelectedThumbnailPosition);
            }
          }
        });
      }

      connectedCallback() {
        this.initializeProductSwapUtility();

        this.onVariantChangeUnsubscriber = subscribe(
          PUB_SUB_EVENTS.optionValueSelectionChange,
          this.handleOptionValueChange.bind(this)
        );

        this.initQuantityHandlers();
        this.attachSecondaryImageListeners();
        this.dispatchEvent(new CustomEvent('product-info:loaded', { bubbles: true }));
      }

      attachSecondaryImageListeners() {
        // Wait for media gallery to be initialized
        setTimeout(() => {
          const mediaGallery = this.querySelector('media-gallery');
          if (!mediaGallery) return;

          // Find all secondary image thumbnails and attach click listeners
          const secondaryThumbnails = mediaGallery.querySelectorAll('[data-target*="secondary-"]');
          secondaryThumbnails.forEach(secondaryThumbnail => {
            const button = secondaryThumbnail.querySelector('button');
            if (button) {
              button.addEventListener('click', () => {
                mediaGallery.setActiveMedia(secondaryThumbnail.dataset.target, false);
              });
            }
          });
        }, 100);
      }

      addPreProcessCallback(callback) {
        this.preProcessHtmlCallbacks.push(callback);
      }

      initQuantityHandlers() {
        if (!this.quantityInput) return;

        this.quantityForm = this.querySelector('.product-form__quantity');
        if (!this.quantityForm) return;

        this.setQuantityBoundries();
        if (!this.dataset.originalSection) {
          this.cartUpdateUnsubscriber = subscribe(PUB_SUB_EVENTS.cartUpdate, this.fetchQuantityRules.bind(this));
        }
      }

      disconnectedCallback() {
        this.onVariantChangeUnsubscriber();
        this.cartUpdateUnsubscriber?.();
      }

      initializeProductSwapUtility() {
        this.preProcessHtmlCallbacks.push((html) =>
          html.querySelectorAll('.scroll-trigger').forEach((element) => element.classList.add('scroll-trigger--cancel'))
        );
        this.postProcessHtmlCallbacks.push((newNode) => {
          window?.Shopify?.PaymentButton?.init();
          window?.ProductModel?.loadShopifyXR();
        });
      }

      handleOptionValueChange({ data: { event, target, selectedOptionValues } }) {
        if (!this.contains(event.target)) return;

        this.resetProductFormState();

        const productUrl = target.dataset.productUrl || this.pendingRequestUrl || this.dataset.url;
        this.pendingRequestUrl = productUrl;
        const shouldSwapProduct = this.dataset.url !== productUrl;
        const shouldFetchFullPage = this.dataset.updateUrl === 'true' && shouldSwapProduct;

        this.renderProductInfo({
          requestUrl: this.buildRequestUrlWithParams(productUrl, selectedOptionValues, shouldFetchFullPage),
          targetId: target.id,
          callback: shouldSwapProduct
            ? this.handleSwapProduct(productUrl, shouldFetchFullPage)
            : this.handleUpdateProductInfo(productUrl),
        });
      }

      resetProductFormState() {
        const productForm = this.productForm;
        productForm?.toggleSubmitButton(true);
        productForm?.handleErrorMessage();
      }

      handleSwapProduct(productUrl, updateFullPage) {
        return (html) => {
          this.productModal?.remove();

          const selector = updateFullPage ? "product-info[id^='MainProduct']" : 'product-info';
          const variant = this.getSelectedVariant(html.querySelector(selector));
          this.updateURL(productUrl, variant?.id);

          if (updateFullPage) {
            document.querySelector('head title').innerHTML = html.querySelector('head title').innerHTML;

            HTMLUpdateUtility.viewTransition(
              document.querySelector('main'),
              html.querySelector('main'),
              this.preProcessHtmlCallbacks,
              this.postProcessHtmlCallbacks
            );
          } else {
            HTMLUpdateUtility.viewTransition(
              this,
              html.querySelector('product-info'),
              this.preProcessHtmlCallbacks,
              this.postProcessHtmlCallbacks
            );
          }
        };
      }

      renderProductInfo({ requestUrl, targetId, callback }) {
        this.abortController?.abort();
        this.abortController = new AbortController();

        fetch(requestUrl, { signal: this.abortController.signal })
          .then((response) => response.text())
          .then((responseText) => {
            this.pendingRequestUrl = null;
            const html = new DOMParser().parseFromString(responseText, 'text/html');
            callback(html);
          })
          .then(() => {
            // set focus to last clicked option value
            document.querySelector(`#${targetId}`)?.focus();
          })
          .catch((error) => {
            if (error.name === 'AbortError') {
              console.log('Fetch aborted by user');
            } else {
              console.error(error);
            }
          });
      }

      getSelectedVariant(productInfoNode) {
        const selectedVariant = productInfoNode.querySelector('variant-selects [data-selected-variant]')?.innerHTML;
        return !!selectedVariant ? JSON.parse(selectedVariant) : null;
      }

      buildRequestUrlWithParams(url, optionValues, shouldFetchFullPage = false) {
        const params = [];

        !shouldFetchFullPage && params.push(`section_id=${this.sectionId}`);

        if (optionValues.length) {
          params.push(`option_values=${optionValues.join(',')}`);
        }

        return `${url}?${params.join('&')}`;
      }

      updateOptionValues(html) {
        const variantSelects = html.querySelector('variant-selects');
        if (variantSelects) {
          HTMLUpdateUtility.viewTransition(this.variantSelectors, variantSelects, this.preProcessHtmlCallbacks);
        }
      }

      handleUpdateProductInfo(productUrl) {
        return (html) => {
          const variant = this.getSelectedVariant(html);

          this.pickupAvailability?.update(variant);
          this.updateOptionValues(html);
          this.updateURL(productUrl, variant?.id);
          this.updateVariantInputs(variant?.id);

          if (!variant) {
            this.setUnavailable();
            return;
          }

          this.updateMedia(html, variant?.featured_media?.id);

          const updateSourceFromDestination = (id, shouldHide = (source) => false) => {
            const source = html.getElementById(`${id}-${this.sectionId}`);
            const destination = this.querySelector(`#${id}-${this.dataset.section}`);
            if (source && destination) {
              destination.innerHTML = source.innerHTML;
              destination.classList.toggle('hidden', shouldHide(source));
            }
          };

          updateSourceFromDestination('price');
          updateSourceFromDestination('Sku', ({ classList }) => classList.contains('hidden'));
          updateSourceFromDestination('Inventory', ({ innerText }) => innerText === '');
          updateSourceFromDestination('Volume');
          updateSourceFromDestination('Price-Per-Item', ({ classList }) => classList.contains('hidden'));

          this.updateQuantityRules(this.sectionId, html);
          this.querySelector(`#Quantity-Rules-${this.dataset.section}`)?.classList.remove('hidden');
          this.querySelector(`#Volume-Note-${this.dataset.section}`)?.classList.remove('hidden');

          this.productForm?.toggleSubmitButton(
            html.getElementById(`ProductSubmitButton-${this.sectionId}`)?.hasAttribute('disabled') ?? true,
            window.variantStrings.soldOut
          );

          publish(PUB_SUB_EVENTS.variantChange, {
            data: {
              sectionId: this.sectionId,
              html,
              variant,
            },
          });

          // Handle secondary image injection
          this.updateSecondaryImage(variant);
        };
      }

      updateVariantInputs(variantId) {
        this.querySelectorAll(
          `#product-form-${this.dataset.section}, #product-form-installment-${this.dataset.section}`
        ).forEach((productForm) => {
          const input = productForm.querySelector('input[name="id"]');
          input.value = variantId ?? '';
          input.dispatchEvent(new Event('change', { bubbles: true }));
        });
      }

      updateURL(url, variantId) {
        this.querySelector('share-button')?.updateUrl(
          `${window.shopUrl}${url}${variantId ? `?variant=${variantId}` : ''}`
        );

        if (this.dataset.updateUrl === 'false') return;
        window.history.replaceState({}, '', `${url}${variantId ? `?variant=${variantId}` : ''}`);
      }

      setUnavailable() {
        this.productForm?.toggleSubmitButton(true, window.variantStrings.unavailable);

        const selectors = ['price', 'Inventory', 'Sku', 'Price-Per-Item', 'Volume-Note', 'Volume', 'Quantity-Rules']
          .map((id) => `#${id}-${this.dataset.section}`)
          .join(', ');
        document.querySelectorAll(selectors).forEach(({ classList }) => classList.add('hidden'));

        // Clear secondary image when variant is unavailable
        this.updateSecondaryImage(null);
      }

      updateMedia(html, variantFeaturedMediaId) {
        if (!variantFeaturedMediaId) return;

        const mediaGallerySource = this.querySelector('media-gallery ul');
        const mediaGalleryDestination = html.querySelector(`media-gallery ul`);

        const refreshSourceData = () => {
          if (this.hasAttribute('data-zoom-on-hover')) enableZoomOnHover(2);
          const mediaGallerySourceItems = Array.from(mediaGallerySource.querySelectorAll('li[data-media-id]'));
          const sourceSet = new Set(mediaGallerySourceItems.map((item) => item.dataset.mediaId));
          const sourceMap = new Map(
            mediaGallerySourceItems.map((item, index) => [item.dataset.mediaId, { item, index }])
          );
          return [mediaGallerySourceItems, sourceSet, sourceMap];
        };

        if (mediaGallerySource && mediaGalleryDestination) {
          let [mediaGallerySourceItems, sourceSet, sourceMap] = refreshSourceData();
          const mediaGalleryDestinationItems = Array.from(
            mediaGalleryDestination.querySelectorAll('li[data-media-id]')
          );
          const destinationSet = new Set(mediaGalleryDestinationItems.map(({ dataset }) => dataset.mediaId));
          let shouldRefresh = false;

          // add items from new data not present in DOM
          for (let i = mediaGalleryDestinationItems.length - 1; i >= 0; i--) {
            if (!sourceSet.has(mediaGalleryDestinationItems[i].dataset.mediaId)) {
              mediaGallerySource.prepend(mediaGalleryDestinationItems[i]);
              shouldRefresh = true;
            }
          }

          // remove items from DOM not present in new data
          for (let i = 0; i < mediaGallerySourceItems.length; i++) {
            if (!destinationSet.has(mediaGallerySourceItems[i].dataset.mediaId)) {
              mediaGallerySourceItems[i].remove();
              shouldRefresh = true;
            }
          }

          // refresh
          if (shouldRefresh) [mediaGallerySourceItems, sourceSet, sourceMap] = refreshSourceData();

          // if media galleries don't match, sort to match new data order
          mediaGalleryDestinationItems.forEach((destinationItem, destinationIndex) => {
            const sourceData = sourceMap.get(destinationItem.dataset.mediaId);

            if (sourceData && sourceData.index !== destinationIndex) {
              mediaGallerySource.insertBefore(
                sourceData.item,
                mediaGallerySource.querySelector(`li:nth-of-type(${destinationIndex + 1})`)
              );

              // refresh source now that it has been modified
              [mediaGallerySourceItems, sourceSet, sourceMap] = refreshSourceData();
            }
          });
        }

        // set featured media as active in the media gallery
        this.querySelector(`media-gallery`)?.setActiveMedia?.(
          `${this.dataset.section}-${variantFeaturedMediaId}`,
          true
        );

        // update media modal
        const modalContent = this.productModal?.querySelector(`.product-media-modal__content`);
        const newModalContent = html.querySelector(`product-modal .product-media-modal__content`);
        if (modalContent && newModalContent) modalContent.innerHTML = newModalContent.innerHTML;
      }

      setQuantityBoundries() {
        const data = {
          cartQuantity: this.quantityInput.dataset.cartQuantity ? parseInt(this.quantityInput.dataset.cartQuantity) : 0,
          min: this.quantityInput.dataset.min ? parseInt(this.quantityInput.dataset.min) : 1,
          max: this.quantityInput.dataset.max ? parseInt(this.quantityInput.dataset.max) : null,
          step: this.quantityInput.step ? parseInt(this.quantityInput.step) : 1,
        };

        let min = data.min;
        const max = data.max === null ? data.max : data.max - data.cartQuantity;
        if (max !== null) min = Math.min(min, max);
        if (data.cartQuantity >= data.min) min = Math.min(min, data.step);

        this.quantityInput.min = min;

        if (max) {
          this.quantityInput.max = max;
        } else {
          this.quantityInput.removeAttribute('max');
        }
        this.quantityInput.value = min;

        publish(PUB_SUB_EVENTS.quantityUpdate, undefined);
      }

      fetchQuantityRules() {
        const currentVariantId = this.productForm?.variantIdInput?.value;
        if (!currentVariantId) return;

        this.querySelector('.quantity__rules-cart .loading__spinner').classList.remove('hidden');
        return fetch(`${this.dataset.url}?variant=${currentVariantId}&section_id=${this.dataset.section}`)
          .then((response) => response.text())
          .then((responseText) => {
            const html = new DOMParser().parseFromString(responseText, 'text/html');
            this.updateQuantityRules(this.dataset.section, html);
          })
          .catch((e) => console.error(e))
          .finally(() => this.querySelector('.quantity__rules-cart .loading__spinner').classList.add('hidden'));
      }

      updateQuantityRules(sectionId, html) {
        if (!this.quantityInput) return;
        this.setQuantityBoundries();

        const quantityFormUpdated = html.getElementById(`Quantity-Form-${sectionId}`);
        const selectors = ['.quantity__input', '.quantity__rules', '.quantity__label'];
        for (let selector of selectors) {
          const current = this.quantityForm.querySelector(selector);
          const updated = quantityFormUpdated.querySelector(selector);
          if (!current || !updated) continue;
          if (selector === '.quantity__input') {
            const attributes = ['data-cart-quantity', 'data-min', 'data-max', 'step'];
            for (let attribute of attributes) {
              const valueUpdated = updated.getAttribute(attribute);
              if (valueUpdated !== null) {
                current.setAttribute(attribute, valueUpdated);
              } else {
                current.removeAttribute(attribute);
              }
            }
          } else {
            current.innerHTML = updated.innerHTML;
          }
        }
      }

      get productForm() {
        return this.querySelector(`product-form`);
      }

      get productModal() {
        return document.querySelector(`#ProductModal-${this.dataset.section}`);
      }

      get pickupAvailability() {
        return this.querySelector(`pickup-availability`);
      }

      get variantSelectors() {
        return this.querySelector('variant-selects');
      }

      get relatedProducts() {
        const relatedProductsSectionId = SectionId.getIdForSection(
          SectionId.parseId(this.sectionId),
          'related-products'
        );
        return document.querySelector(`product-recommendations[data-section-id^="${relatedProductsSectionId}"]`);
      }

      get quickOrderList() {
        const quickOrderListSectionId = SectionId.getIdForSection(
          SectionId.parseId(this.sectionId),
          'quick_order_list'
        );
        return document.querySelector(`quick-order-list[data-id^="${quickOrderListSectionId}"]`);
      }

      get sectionId() {
        return this.dataset.originalSection || this.dataset.section;
      }

      updateSecondaryImage(variant) {
        const mediaGallery = this.querySelector('media-gallery');
        if (!mediaGallery) return;

        // Hide all secondary images first
        const allSecondaryImages = mediaGallery.querySelectorAll('[data-media-id*="secondary-"]');
        const allSecondaryThumbnails = mediaGallery.querySelectorAll('[data-target*="secondary-"]');

        allSecondaryImages.forEach(img => {
          img.style.display = 'none';
          img.classList.add('secondary-image-hidden');
        });

        allSecondaryThumbnails.forEach(thumb => {
          thumb.style.display = 'none';
          thumb.classList.add('secondary-thumbnail-hidden');
        });

        console.log('Hidden all secondary images and thumbnails');

        // Show secondary image for current variant if it exists
        if (variant && variant.id) {
          const currentSecondaryImage = mediaGallery.querySelector(`[data-media-id="${this.sectionId}-secondary-${variant.id}"]`);
          const currentSecondaryThumbnail = mediaGallery.querySelector(`[data-target="${this.sectionId}-secondary-${variant.id}"]`);

          console.log('Looking for secondary image with ID:', `${this.sectionId}-secondary-${variant.id}`);
          console.log('Found secondary image:', !!currentSecondaryImage);
          console.log('Found secondary thumbnail:', !!currentSecondaryThumbnail);

          if (currentSecondaryImage) {
            currentSecondaryImage.style.display = '';
            currentSecondaryImage.style.removeProperty('display');
            currentSecondaryImage.classList.remove('secondary-image-hidden');
            console.log('Showed secondary image, display now:', window.getComputedStyle(currentSecondaryImage).display);
          }

          if (currentSecondaryThumbnail) {
            currentSecondaryThumbnail.style.display = '';
            currentSecondaryThumbnail.style.removeProperty('display');
            currentSecondaryThumbnail.classList.remove('secondary-thumbnail-hidden');
            console.log('Showed secondary thumbnail');
          }
        }

        // Re-attach click listeners to visible thumbnails
        this.reattachThumbnailListeners();

        // Restore thumbnail selection for smooth UX
        this.restoreThumbnailSelection(variant);
      }



      reattachThumbnailListeners() {
        const mediaGallery = this.querySelector('media-gallery');
        if (!mediaGallery || !mediaGallery.elements.thumbnails) return;

        // Re-attach click listeners to all visible thumbnails
        mediaGallery.elements.thumbnails.querySelectorAll('[data-target]:not(.secondary-thumbnail-hidden)').forEach((mediaToSwitch) => {
          const button = mediaToSwitch.querySelector('button');
          if (button) {
            // Remove existing listeners by cloning the button
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add new listener
            newButton.addEventListener('click', () => {
              mediaGallery.setActiveMedia(mediaToSwitch.dataset.target, false);
            });
          }
        });
      }

      restoreThumbnailSelection(variant) {
        const mediaGallery = this.querySelector('media-gallery');
        if (!mediaGallery) return;

        // Get all currently visible thumbnails after variant change
        const visibleThumbnails = Array.from(this.querySelectorAll('.thumbnail-list__item')).filter(thumb => {
          const style = window.getComputedStyle(thumb);
          return style.display !== 'none';
        });

        console.log('Visible thumbnails after variant change:', visibleThumbnails.length);
        console.log('Visible thumbnail targets:', visibleThumbnails.map(t => t.dataset.target));
        console.log('Trying to restore position:', this.lastSelectedThumbnailPosition);

        let targetMediaId = null;
        let targetThumbnailButton = null;

        // Try to restore the same thumbnail position
        if (this.lastSelectedThumbnailPosition < visibleThumbnails.length) {
          const targetThumbnail = visibleThumbnails[this.lastSelectedThumbnailPosition];
          targetMediaId = targetThumbnail?.dataset.target;
          targetThumbnailButton = targetThumbnail?.querySelector('.thumbnail');

          console.log('Found target thumbnail at position:', this.lastSelectedThumbnailPosition);
          console.log('Target media ID:', targetMediaId);
        } else {
          console.log('Position', this.lastSelectedThumbnailPosition, 'not available in', visibleThumbnails.length, 'visible thumbnails');
        }

        // Fallback to variant's featured image if position restoration fails
        if (!targetMediaId && variant.featured_media?.id) {
          targetMediaId = `${this.sectionId}-${variant.featured_media.id}`;
          targetThumbnailButton = this.querySelector(`[data-target="${targetMediaId}"] .thumbnail`);
        }

        if (targetMediaId && targetThumbnailButton) {
          // Pre-emptively set the correct thumbnail as active to prevent flicker
          this.setThumbnailActive(targetThumbnailButton);

          // Use minimal delay to override Shopify's media selection smoothly
          setTimeout(() => {
            console.log('Restoring media with ID:', targetMediaId);
            const targetMediaElement = mediaGallery.querySelector(`[data-media-id="${targetMediaId}"]`);
            console.log('Target media element found:', !!targetMediaElement);
            if (targetMediaElement) {
              console.log('Target media element display:', window.getComputedStyle(targetMediaElement).display);
            }
            mediaGallery.setActiveMedia(targetMediaId, false);
          }, 10);
        }
      }

      setThumbnailActive(targetButton) {
        // Remove aria-current from all thumbnails
        this.querySelectorAll('.thumbnail').forEach(thumb => {
          thumb.removeAttribute('aria-current');
        });

        // Set the target thumbnail as active
        if (targetButton) {
          targetButton.setAttribute('aria-current', 'true');
        }
      }






    }
  );
}
