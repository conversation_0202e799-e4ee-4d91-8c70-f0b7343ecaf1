# Wildbreed Theme

This repo contains the custom Shopify theme code for [WildBreed Co.](https://wildbreedco.myshopify.com), built on top of [Dawn v15.3.0](https://github.com/Shopify/dawn/releases/tag/v15.3.0).

## 🧰 Requirements

- [Shopify CLI v3](https://shopify.dev/docs/themes/tools/cli)
- [Node.js](https://nodejs.org/) (for theme linting)
- Git + GitHub (optional but recommended)

## 🔧 Setup

Clone the repository and install dependencies:

```bash
<NAME_EMAIL>:yourusername/wildbreed-theme.git
cd wildbreed-theme
npm install
```

*Note: You may need to update the `STORE`, `DEV_THEME_ID`, and `LIVE_THEME_ID` variables in the `Makefile` to match your environment.*

## 💡 Workflow

This project uses a `Makefile` to simplify common Shopify CLI commands.

| Command | Description |
| :--- | :--- |
| `make list` | List themes on the store. |
| `make pull-dev` | Pulls the latest version of the development theme from Shopify. |
| `make push-dev` | Pushes your local changes to the development theme on Shopify. |
| `make preview` | Opens a live preview of the development theme in your browser. |
| `make push-live` | **(Use with caution)** Pushes your local changes directly to the live theme. |
| `make diff` | Compares the current theme against the base Dawn v15.3.0 theme. |
| `make lint` | Runs theme-check linter to identify code quality issues (requires Shopify CLI). |
| `make lint-fix` | Runs theme-check linter with auto-fix for correctable issues (requires Shopify CLI). |

---

### 🔍 Theme Linting

This project uses [Shopify's theme-check](https://shopify.dev/docs/themes/tools/theme-check) to maintain code quality and follow Shopify theme best practices.

Run the linter to check for issues:

```bash
make lint          # Check for issues
make lint-fix      # Auto-fix correctable issues
```

The linter checks for:

- Liquid syntax errors
- Performance issues
- Accessibility problems
- Security vulnerabilities
- Code style consistency

**GitHub Actions:** Theme checking runs automatically on pull requests using the official [Shopify theme-check-action](https://github.com/Shopify/theme-check-action).

### 🧪 Diff Against Base Theme

To compare this theme against an unmodified version of Dawn v15.3.0, first clone the base theme into a parallel directory:

```bash
# In the parent directory of wildbreed-theme/
git clone --branch v15.3.0 --depth 1 https://github.com/Shopify/dawn.git dawn-15.3.0
```

Then, from within the `wildbreed-theme` directory, run the diff command:

```bash
make diff
```

### 🗂️ Folder Structure

- `sections/`, `snippets/`, `templates/`: Core Liquid template files and reusable components.
- `assets/`: All static assets, including CSS, JavaScript, images, and fonts.
- `locales/`: JSON files for translations and localization.
- `config/`: Global theme settings (`settings_schema.json`) and data (`settings_data.json`).

### 🚀 Deployment

When you are ready to go live, you can either duplicate the development theme in the Shopify Admin panel or push directly to the live theme ID using the make command.

**Pushing directly to live is a destructive action and should be done with caution.**

```bash
make push-live
```
