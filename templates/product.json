/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "vendor": {
          "type": "text",
          "settings": {
            "text": "{{ product.vendor }}",
            "text_style": "uppercase"
          }
        },
        "title": {
          "type": "title",
          "settings": {}
        },
        "caption": {
          "type": "text",
          "settings": {
            "text": "{{ product.metafields.descriptors.subtitle.value }}",
            "text_style": "subtitle"
          }
        },
        "price": {
          "type": "price",
          "settings": {}
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "picker_type": "button",
            "swatch_shape": "circle"
          }
        },
        "quantity_selector": {
          "type": "quantity_selector",
          "settings": {}
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": true,
            "show_gift_card_recipient": true
          }
        },
        "description": {
          "type": "description",
          "settings": {}
        },
        "collapsible-row-0": {
          "type": "collapsible_tab",
          "disabled": true,
          "settings": {
            "heading": "Materials",
            "icon": "leather",
            "content": "<p>Suede</p>",
            "page": ""
          }
        },
        "collapsible-row-2": {
          "type": "collapsible_tab",
          "disabled": true,
          "settings": {
            "heading": "Dimensions",
            "icon": "ruler",
            "content": "",
            "page": ""
          }
        },
        "collapsible-row-1": {
          "type": "collapsible_tab",
          "disabled": true,
          "settings": {
            "heading": "Shipping & Returns",
            "icon": "truck",
            "content": "",
            "page": ""
          }
        },
        "collapsible-row-3": {
          "type": "collapsible_tab",
          "disabled": true,
          "settings": {
            "heading": "Care Instructions",
            "icon": "heart",
            "content": "",
            "page": ""
          }
        },
        "share": {
          "type": "share",
          "settings": {
            "share_label": "Share"
          }
        }
      },
      "block_order": [
        "vendor",
        "title",
        "caption",
        "price",
        "variant_picker",
        "quantity_selector",
        "buy_buttons",
        "description",
        "collapsible-row-0",
        "collapsible-row-2",
        "collapsible-row-1",
        "collapsible-row-3",
        "share"
      ],
      "settings": {
        "enable_sticky_info": true,
        "color_scheme": "",
        "media_size": "large",
        "constrain_to_viewport": true,
        "media_fit": "contain",
        "gallery_layout": "thumbnail_slider",
        "mobile_thumbnails": "hide",
        "media_position": "left",
        "image_zoom": "lightbox",
        "hide_variants": true,
        "enable_video_looping": false,
        "padding_top": 36,
        "padding_bottom": 12
      }
    },
    "related-products": {
      "type": "related-products",
      "settings": {
        "heading": "You may also like",
        "heading_size": "h2",
        "products_to_show": 4,
        "columns_desktop": 4,
        "columns_mobile": "2",
        "color_scheme": "scheme-1",
        "image_ratio": "square",
        "image_shape": "default",
        "show_secondary_image": true,
        "show_vendor": false,
        "show_rating": false,
        "padding_top": 36,
        "padding_bottom": 28
      }
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "settings": {
        "heading": "You may also like",
        "heading_size": "h2",
        "products_to_show": 4,
        "columns_desktop": 4,
        "color_scheme": "background-1",
        "image_ratio": "square",
        "show_secondary_image": true,
        "show_vendor": false,
        "show_rating": false,
        "columns_mobile": "2",
        "padding_top": 36,
        "padding_bottom": 28
      }
    }
  },
  "order": [
    "main",
    "related-products",
    "product-recommendations"
  ]
}
