/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "cart-items": {
      "type": "main-cart-items",
      "settings": {
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "cart-footer": {
      "type": "main-cart-footer",
      "blocks": {
        "subtotal": {
          "type": "subtotal",
          "settings": {}
        },
        "buttons": {
          "type": "buttons",
          "settings": {}
        }
      },
      "block_order": [
        "subtotal",
        "buttons"
      ],
      "settings": {}
    },
    "featured-collection": {
      "type": "featured-collection",
      "settings": {
        "collection": "all",
        "products_to_show": 4,
        "title": "Featured collection",
        "heading_size": "h2",
        "columns_desktop": 4,
        "show_view_all": true,
        "color_scheme": "background-1",
        "image_ratio": "square",
        "show_secondary_image": false,
        "show_vendor": false,
        "show_rating": false,
        "columns_mobile": "2",
        "swipe_on_mobile": false,
        "padding_top": 36,
        "padding_bottom": 36
      }
    }
  },
  "order": [
    "cart-items",
    "cart-footer",
    "featured-collection"
  ]
}
