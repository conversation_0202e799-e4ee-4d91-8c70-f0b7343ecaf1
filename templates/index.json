/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "image_banner": {
      "type": "image-banner",
      "blocks": {
        "heading": {
          "type": "heading",
          "settings": {
            "heading": "Find your look",
            "heading_size": "h0"
          }
        },
        "button": {
          "type": "buttons",
          "settings": {
            "button_label_1": "Shop the Collection",
            "button_link_1": "shopify://collections/all",
            "button_style_secondary_1": true,
            "button_label_2": "",
            "button_link_2": "",
            "button_style_secondary_2": false
          }
        }
      },
      "block_order": [
        "heading",
        "button"
      ],
      "disabled": true,
      "settings": {
        "image": "shopify://shop_images/274A9887-banner-3280x1845.jpg",
        "image_overlay_opacity": 40,
        "image_height": "medium",
        "image_behavior": "none",
        "desktop_content_position": "bottom-center",
        "desktop_content_alignment": "center",
        "show_text_box": false,
        "color_scheme": "background-1",
        "stack_images_on_mobile": false,
        "mobile_content_alignment": "center",
        "show_text_below": false
      }
    },
    "rich_text": {
      "type": "rich-text",
      "blocks": {
        "heading": {
          "type": "heading",
          "settings": {
            "heading": "The WildBreed Collection",
            "heading_size": "h2"
          }
        },
        "text": {
          "type": "text",
          "settings": {
            "text": "<p>A brand Born in Texas, raised in California. </p><p>View all of our latest styles to match your look.</p>"
          }
        },
        "button_yiTzgU": {
          "type": "button",
          "settings": {
            "button_label": "Shop All",
            "button_link": "",
            "button_style_secondary": true,
            "button_label_2": "",
            "button_link_2": "",
            "button_style_secondary_2": false
          }
        }
      },
      "block_order": [
        "heading",
        "text",
        "button_yiTzgU"
      ],
      "settings": {
        "desktop_content_position": "center",
        "content_alignment": "center",
        "color_scheme": "background-1",
        "full_width": true,
        "padding_top": 40,
        "padding_bottom": 0
      }
    },
    "featured_collection": {
      "type": "featured-collection",
      "settings": {
        "collection": "all",
        "products_to_show": 8,
        "title": "",
        "heading_size": "h2",
        "description": "",
        "show_description": false,
        "description_style": "body",
        "columns_desktop": 4,
        "enable_desktop_slider": false,
        "full_width": false,
        "show_view_all": true,
        "view_all_style": "solid",
        "color_scheme": "background-1",
        "image_ratio": "adapt",
        "image_shape": "default",
        "show_secondary_image": true,
        "show_vendor": false,
        "show_rating": false,
        "quick_add": "none",
        "columns_mobile": "2",
        "swipe_on_mobile": false,
        "padding_top": 28,
        "padding_bottom": 36
      }
    },
    "collage": {
      "type": "collage",
      "blocks": {
        "collection-0": {
          "type": "collection",
          "settings": {
            "collection": "the-wildbreed-classics-collection"
          }
        },
        "collection-1": {
          "type": "collection",
          "settings": {
            "collection": "the-rare-breed-collection"
          }
        }
      },
      "block_order": [
        "collection-0",
        "collection-1"
      ],
      "disabled": true,
      "settings": {
        "heading": "Collections",
        "heading_size": "h2",
        "desktop_layout": "left",
        "mobile_layout": "collage",
        "card_styles": "product-card-wrapper",
        "color_scheme": "background-1",
        "padding_top": 36,
        "padding_bottom": 36
      }
    },
    "89eadf13-af3d-4839-abb6-73cebf673ac7": {
      "type": "rich-text",
      "blocks": {
        "template--15980775014540__89eadf13-af3d-4839-abb6-73cebf673ac7-16870726284cf13923-0": {
          "type": "heading",
          "disabled": true,
          "settings": {
            "heading": "Talk about your brand",
            "heading_size": "h1"
          }
        },
        "template--15980775014540__89eadf13-af3d-4839-abb6-73cebf673ac7-16870726284cf13923-1": {
          "type": "text",
          "disabled": true,
          "settings": {
            "text": "<p>Share information about your brand with your customers. Describe a product, make announcements, or welcome customers to your store.</p>"
          }
        },
        "template--15980775014540__89eadf13-af3d-4839-abb6-73cebf673ac7-16870726284cf13923-2": {
          "type": "button",
          "settings": {
            "button_label": "Shop All",
            "button_link": "shopify://collections/all",
            "button_style_secondary": true,
            "button_label_2": "",
            "button_link_2": "",
            "button_style_secondary_2": false
          }
        }
      },
      "block_order": [
        "template--15980775014540__89eadf13-af3d-4839-abb6-73cebf673ac7-16870726284cf13923-0",
        "template--15980775014540__89eadf13-af3d-4839-abb6-73cebf673ac7-16870726284cf13923-1",
        "template--15980775014540__89eadf13-af3d-4839-abb6-73cebf673ac7-16870726284cf13923-2"
      ],
      "settings": {
        "desktop_content_position": "center",
        "content_alignment": "center",
        "color_scheme": "background-1",
        "full_width": true,
        "padding_top": 40,
        "padding_bottom": 52
      }
    },
    "be49d681-4797-44a0-83c7-eea918b74e7f": {
      "type": "image-with-text",
      "blocks": {
        "template--15980775014540__be49d681-4797-44a0-83c7-eea918b74e7f-1687073070f9f09239-0": {
          "type": "heading",
          "settings": {
            "heading": "Solana",
            "heading_size": "h1"
          }
        },
        "template--15980775014540__be49d681-4797-44a0-83c7-eea918b74e7f-1687073070f9f09239-1": {
          "type": "text",
          "settings": {
            "text": "<p>Our most classic style.</p>",
            "text_style": "body"
          }
        },
        "template--15980775014540__be49d681-4797-44a0-83c7-eea918b74e7f-1687073070f9f09239-2": {
          "type": "button",
          "settings": {
            "button_label": "View all colors",
            "button_link": "shopify://collections/solana",
            "button_style_secondary": false
          }
        }
      },
      "block_order": [
        "template--15980775014540__be49d681-4797-44a0-83c7-eea918b74e7f-1687073070f9f09239-0",
        "template--15980775014540__be49d681-4797-44a0-83c7-eea918b74e7f-1687073070f9f09239-1",
        "template--15980775014540__be49d681-4797-44a0-83c7-eea918b74e7f-1687073070f9f09239-2"
      ],
      "disabled": true,
      "settings": {
        "image": "shopify://shop_images/solana-ivory-angle-1.png",
        "height": "adapt",
        "desktop_image_width": "medium",
        "layout": "image_first",
        "image_behavior": "none",
        "content_layout": "no-overlap",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "mobile_content_alignment": "center",
        "section_color_scheme": "",
        "color_scheme": "background-1",
        "padding_top": 20,
        "padding_bottom": 28
      }
    }
  },
  "order": [
    "image_banner",
    "rich_text",
    "featured_collection",
    "collage",
    "89eadf13-af3d-4839-abb6-73cebf673ac7",
    "be49d681-4797-44a0-83c7-eea918b74e7f"
  ]
}
