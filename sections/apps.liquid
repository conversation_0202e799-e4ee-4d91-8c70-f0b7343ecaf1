<div class="{% if section.settings.include_margins %}page-width{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
  {%- for block in section.blocks -%}
    {% render block %}
  {%- endfor -%}
</div>

{% schema %}
{
  "name": "t:sections.apps.name",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "checkbox",
      "id": "include_margins",
      "default": true,
      "label": "t:sections.apps.settings.include_margins.label"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.apps.presets.name"
    }
  ]
}
{% endschema %}
