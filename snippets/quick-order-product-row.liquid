{% comment %}
  Quick Order Product Row
  Accepts:
  - item: {Object} Variant or Product object
  - image: {Object} Product or Variant image (optional)
  Usage:
  {% render 'quick-order-product-row' %}
{% endcomment %}

<tr
  class="product"
>
  <td class="product-item__inner">
    <div class="product-item__title--wrapper">
      <div class="variant-item__media">
        <div class="variant-item__image-container gradient global-media-settings">
          {%- if image -%}
            {%- assign img_height = 36 | divided_by: image.aspect_ratio | ceil -%}
            {{
              image
              | image_url: width: 72
              | image_tag:
                loading: 'lazy',
                fetchpriority: 'low',
                decoding: 'async',
                class: 'variant-item__image',
                width: 36,
                height: img_height,
                widths: '72',
                alt: image.alt
              | escape
            }}
          {% endif %}
        </div>
      </div>
      <div class="small-hide">
        <a href="{{ item.url }}" class="variant-item__name h4 break">{{ item.title | escape }}</a>
      </div>
    </div>
  </td>
</tr>
