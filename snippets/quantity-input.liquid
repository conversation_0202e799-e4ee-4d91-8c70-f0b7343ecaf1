{% comment %}
  Quantity input

  Accepts:
  - variant: {Object} Variant object
  - variant_id: {String} Variant ID (optional)
  - min: {Number} Min (optional)

  Usage:
  {% render 'quantity-input' variant: variant %}
{% endcomment %}

<quantity-input class="quantity cart-quantity">
  <button class="quantity__button" name="minus" type="button" data-target="decrement-{{ variant.id }}">
    <span class="visually-hidden">
      {{- 'products.product.quantity.decrease' | t: product: variant.title | escape -}}
    </span>
    <span class="svg-wrapper">{{ 'icon-minus.svg' | inline_asset_content }}</span>
  </button>
  <input
    class="quantity__input"
    data-quantity-variant-id="{{ variant.id }}"
    type="number"
    name="updates[{{ variant_id }}]"
    {% # theme-check-disable %}
    value="{{ cart | item_count_for_variant: variant.id }}"
    data-cart-quantity="{{ cart | item_count_for_variant: variant.id }}"
    min="{{ min }}"
    data-min="{{ variant.quantity_rule.min }}"
    {% if variant.quantity_rule.max != null %}
      max="{{ variant.quantity_rule.max }}"
    {% endif %}
    step="{{ variant.quantity_rule.increment }}"
    {% # theme-check-enable %}
    aria-label="{{ 'products.product.quantity.input_label' | t: product: variant.title | escape }}"
    id="Quantity-{{ variant.id }}"
    data-index="{{ variant.id }}"
    data-target="quantity-input-{{ variant.id }}"
  >
  <button class="quantity__button" name="plus" type="button" data-target="increment-{{ variant.id }}">
    <span class="visually-hidden">
      {{- 'products.product.quantity.increase' | t: product: variant.title | escape -}}
    </span>
    <span class="svg-wrapper">{{ 'icon-plus.svg' | inline_asset_content }}</span>
  </button>
  {%- render 'progress-bar' -%}
</quantity-input>
